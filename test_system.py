# -*- coding: utf-8 -*-
"""
اختبار شامل لنظام محاسبة الصيدلية الزراعية
"""

import sys
import os
import traceback
from datetime import datetime

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار استيراد جميع الوحدات"""
    print("🔍 اختبار استيراد الوحدات...")

    try:
        # اختبار الإعدادات
        import config.settings as settings
        print("✅ تم استيراد الإعدادات بنجاح")
        
        # اختبار قاعدة البيانات
        from database.database import PharmacyDatabase
        from database.models import DatabaseManager
        print("✅ تم استيراد وحدات قاعدة البيانات بنجاح")
        
        # اختبار الواجهات
        from gui.main_window import MainWindow
        from gui.customers import CustomersWindow
        from gui.suppliers import SuppliersWindow
        from gui.inventory import InventoryWindow
        from gui.sales import NewSaleWindow, SalesWindow
        print("✅ تم استيراد واجهات المستخدم بنجاح")
        
        # اختبار الأدوات المساعدة
        from utils.backup import BackupManager
        from utils.autocomplete import AutoCompleteEntry, DataValidator
        print("✅ تم استيراد الأدوات المساعدة بنجاح")
        
        # اختبار نوافذ التفاصيل
        from gui.supplier_details import SupplierDetailsWindow
        from gui.settings_window import SettingsWindow
        print("✅ تم استيراد النوافذ المتقدمة بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {str(e)}")
        traceback.print_exc()
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🗄️ اختبار قاعدة البيانات...")
    
    try:
        from database.database import PharmacyDatabase
        
        # إنشاء قاعدة بيانات اختبار
        db = PharmacyDatabase("test_pharmacy.db")
        print("✅ تم إنشاء قاعدة البيانات بنجاح")
        
        # اختبار إضافة مورد
        supplier_id = db.add_supplier("مورد اختبار", "0501234567", "الرياض", "<EMAIL>")
        print(f"✅ تم إضافة مورد بالمعرف: {supplier_id}")
        
        # اختبار إضافة منتج
        product_id = db.add_product("منتج اختبار", "أدوية", "حبة", 10.0, 15.0, 100, 10, None, supplier_id)
        print(f"✅ تم إضافة منتج بالمعرف: {product_id}")
        
        # اختبار إضافة زبون
        customer_id = db.add_customer("زبون اختبار", "0509876543", "جدة", "<EMAIL>", 1000.0)
        print(f"✅ تم إضافة زبون بالمعرف: {customer_id}")
        
        # اختبار إضافة بيع
        sale_id = db.add_sale(customer_id, 150.0, 100.0, "نقدي", "بيع اختبار")
        print(f"✅ تم إضافة بيع بالمعرف: {sale_id}")
        
        # اختبار إضافة عنصر بيع
        item_id = db.add_sale_item(sale_id, product_id, 10, 15.0)
        print(f"✅ تم إضافة عنصر بيع بالمعرف: {item_id}")
        
        # اختبار إضافة دفعة
        payment_id = db.add_payment(customer_id, None, 50.0, "استلام", "نقدي", "دفعة اختبار")
        print(f"✅ تم إضافة دفعة بالمعرف: {payment_id}")
        
        # اختبار الاستعلامات
        products = db.get_all_products()
        customers = db.get_all_customers()
        suppliers = db.get_all_suppliers()
        sales = db.get_all_sales()
        
        print(f"✅ المنتجات: {len(products)}, الزبائن: {len(customers)}, الموردين: {len(suppliers)}, المبيعات: {len(sales)}")
        
        # تنظيف ملف الاختبار
        os.remove("test_pharmacy.db")
        print("✅ تم حذف قاعدة بيانات الاختبار")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {str(e)}")
        traceback.print_exc()
        return False

def test_backup_system():
    """اختبار نظام النسخ الاحتياطية"""
    print("\n💾 اختبار نظام النسخ الاحتياطية...")
    
    try:
        from utils.backup import BackupManager
        from database.database import PharmacyDatabase
        
        # إنشاء قاعدة بيانات اختبار
        db = PharmacyDatabase("test_backup.db")
        db.add_supplier("مورد للنسخ الاحتياطي", "0501111111")
        
        # اختبار النسخ الاحتياطي
        backup_manager = BackupManager(db, "test_backup.db")
        backup_path = backup_manager.create_backup("test_backup")
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
        
        # اختبار التحقق من صحة النسخة
        is_valid = backup_manager.validate_database(backup_path)
        print(f"✅ صحة النسخة الاحتياطية: {is_valid}")
        
        # اختبار قائمة النسخ
        backups = backup_manager.get_backup_list()
        print(f"✅ عدد النسخ الاحتياطية: {len(backups)}")
        
        # تنظيف ملفات الاختبار
        os.remove("test_backup.db")
        if os.path.exists(backup_path):
            os.remove(backup_path)
        print("✅ تم حذف ملفات الاختبار")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نظام النسخ الاحتياطية: {str(e)}")
        traceback.print_exc()
        return False

def test_validation():
    """اختبار نظام التحقق من البيانات"""
    print("\n🔍 اختبار نظام التحقق من البيانات...")
    
    try:
        from utils.autocomplete import DataValidator
        
        # اختبار التحقق من الهاتف
        valid_phones = ["0501234567", "966501234567", "+966501234567"]
        invalid_phones = ["123", "0401234567", "abc"]
        
        for phone in valid_phones:
            if DataValidator.validate_phone(phone):
                print(f"✅ هاتف صحيح: {phone}")
            else:
                print(f"❌ هاتف خاطئ: {phone}")
        
        for phone in invalid_phones:
            if not DataValidator.validate_phone(phone):
                print(f"✅ تم رفض هاتف خاطئ: {phone}")
            else:
                print(f"❌ تم قبول هاتف خاطئ: {phone}")
        
        # اختبار التحقق من البريد الإلكتروني
        valid_emails = ["<EMAIL>", "<EMAIL>"]
        invalid_emails = ["invalid-email", "@domain.com", "user@"]
        
        for email in valid_emails:
            if DataValidator.validate_email(email):
                print(f"✅ بريد إلكتروني صحيح: {email}")
            else:
                print(f"❌ بريد إلكتروني خاطئ: {email}")
        
        for email in invalid_emails:
            if not DataValidator.validate_email(email):
                print(f"✅ تم رفض بريد إلكتروني خاطئ: {email}")
            else:
                print(f"❌ تم قبول بريد إلكتروني خاطئ: {email}")
        
        # اختبار تنسيق الأرقام
        test_numbers = [1234.56, 1000000.789, 0.5]
        for num in test_numbers:
            formatted = DataValidator.format_currency(num)
            print(f"✅ تنسيق الرقم {num}: {formatted}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نظام التحقق: {str(e)}")
        traceback.print_exc()
        return False

def test_reports():
    """اختبار نظام التقارير"""
    print("\n📊 اختبار نظام التقارير...")
    
    try:
        from utils.reports import PDFReportGenerator, REPORTLAB_AVAILABLE
        
        if not REPORTLAB_AVAILABLE:
            print("⚠️ مكتبة reportlab غير متوفرة - سيتم تخطي اختبار التقارير")
            return True
        
        from database.database import PharmacyDatabase
        
        # إنشاء قاعدة بيانات اختبار
        db = PharmacyDatabase("test_reports.db")
        
        # إضافة بيانات اختبار
        supplier_id = db.add_supplier("مورد التقارير", "0501111111")
        customer_id = db.add_customer("زبون التقارير", "0502222222", credit_limit=1000.0)
        product_id = db.add_product("منتج التقارير", "أدوية", "حبة", 10.0, 15.0, 100, 10, None, supplier_id)
        
        sale_id = db.add_sale(customer_id, 150.0, 100.0, "نقدي")
        db.add_sale_item(sale_id, product_id, 10, 15.0)
        db.add_payment(customer_id, None, 50.0, "استلام", "نقدي")
        
        # اختبار إنشاء التقارير
        report_generator = PDFReportGenerator(db)
        print("✅ تم إنشاء مولد التقارير بنجاح")
        
        # تنظيف ملف الاختبار
        os.remove("test_reports.db")
        print("✅ تم حذف قاعدة بيانات الاختبار")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نظام التقارير: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء الاختبار الشامل لنظام محاسبة الصيدلية الزراعية")
    print("=" * 60)
    
    tests = [
        ("استيراد الوحدات", test_imports),
        ("قاعدة البيانات", test_database),
        ("نظام النسخ الاحتياطية", test_backup_system),
        ("نظام التحقق من البيانات", test_validation),
        ("نظام التقارير", test_reports)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ نجح اختبار: {test_name}")
                passed += 1
            else:
                print(f"❌ فشل اختبار: {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {str(e)}")
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
