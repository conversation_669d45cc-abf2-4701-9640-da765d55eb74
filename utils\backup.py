# -*- coding: utf-8 -*-
"""
وحدة النسخ الاحتياطية لبرنامج محاسبة الصيدلية الزراعية
"""

import os
import shutil
import sqlite3
from datetime import datetime, timedelta
from typing import List, Optional
import sys

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import *

class BackupManager:
    """مدير النسخ الاحتياطية"""

    def __init__(self, db=None, db_path: str = DATABASE_PATH):
        self.db = db
        self.db_path = db_path
        self.backup_folder = BACKUP_FOLDER
        ensure_folders_exist()
    
    def create_backup(self, custom_name: str = None) -> str:
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            # التأكد من وجود قاعدة البيانات
            if not os.path.exists(self.db_path):
                raise FileNotFoundError("قاعدة البيانات غير موجودة")
            
            # إنشاء اسم الملف
            if custom_name:
                backup_filename = f"{custom_name}.db"
            else:
                backup_filename = get_backup_filename()
            
            backup_path = os.path.join(self.backup_folder, backup_filename)
            
            # نسخ قاعدة البيانات
            shutil.copy2(self.db_path, backup_path)
            
            # تنظيف النسخ القديمة
            self.cleanup_old_backups()
            
            return backup_path
            
        except Exception as e:
            raise Exception(f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")
    
    def restore_backup(self, backup_path: str) -> bool:
        """استعادة نسخة احتياطية"""
        try:
            # التأكد من وجود ملف النسخة الاحتياطية
            if not os.path.exists(backup_path):
                raise FileNotFoundError("ملف النسخة الاحتياطية غير موجود")
            
            # التحقق من صحة قاعدة البيانات
            if not self.validate_database(backup_path):
                raise Exception("ملف النسخة الاحتياطية تالف")
            
            # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
            current_backup = self.create_backup("before_restore")
            
            # استعادة النسخة الاحتياطية
            shutil.copy2(backup_path, self.db_path)
            
            return True
            
        except Exception as e:
            raise Exception(f"فشل في استعادة النسخة الاحتياطية: {str(e)}")
    
    def get_backup_list(self) -> List[dict]:
        """الحصول على قائمة النسخ الاحتياطية"""
        backups = []
        
        if not os.path.exists(self.backup_folder):
            return backups
        
        for filename in os.listdir(self.backup_folder):
            if filename.endswith('.db'):
                file_path = os.path.join(self.backup_folder, filename)
                file_stats = os.stat(file_path)
                
                backup_info = {
                    'filename': filename,
                    'path': file_path,
                    'size': file_stats.st_size,
                    'created_date': datetime.fromtimestamp(file_stats.st_ctime),
                    'modified_date': datetime.fromtimestamp(file_stats.st_mtime)
                }
                backups.append(backup_info)
        
        # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
        backups.sort(key=lambda x: x['created_date'], reverse=True)
        
        return backups
    
    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backups = self.get_backup_list()
            
            # حذف النسخ الزائدة عن الحد المسموح
            if len(backups) > MAX_BACKUP_FILES:
                for backup in backups[MAX_BACKUP_FILES:]:
                    os.remove(backup['path'])
            
            # حذف النسخ الأقدم من المدة المحددة
            cutoff_date = datetime.now() - timedelta(days=BACKUP_INTERVAL_DAYS * 5)
            for backup in backups:
                if backup['created_date'] < cutoff_date:
                    os.remove(backup['path'])
                    
        except Exception as e:
            print(f"خطأ في تنظيف النسخ الاحتياطية: {str(e)}")
    
    def validate_database(self, db_path: str) -> bool:
        """التحقق من صحة قاعدة البيانات"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود الجداول الأساسية
            required_tables = ['products', 'customers', 'suppliers', 'sales', 'purchases']
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            existing_tables = [row[0] for row in cursor.fetchall()]
            
            for table in required_tables:
                if table not in existing_tables:
                    conn.close()
                    return False
            
            conn.close()
            return True
            
        except Exception:
            return False
    
    def auto_backup(self) -> Optional[str]:
        """إنشاء نسخة احتياطية تلقائية"""
        if not AUTO_BACKUP:
            return None
        
        try:
            # التحقق من آخر نسخة احتياطية
            backups = self.get_backup_list()
            
            if backups:
                last_backup_date = backups[0]['created_date']
                days_since_backup = (datetime.now() - last_backup_date).days
                
                if days_since_backup < BACKUP_INTERVAL_DAYS:
                    return None  # لا حاجة لنسخة احتياطية جديدة
            
            # إنشاء نسخة احتياطية تلقائية
            return self.create_backup("auto_backup")
            
        except Exception as e:
            print(f"خطأ في النسخ الاحتياطي التلقائي: {str(e)}")
            return None
    
    def get_backup_size(self, backup_path: str) -> str:
        """الحصول على حجم النسخة الاحتياطية بصيغة قابلة للقراءة"""
        try:
            size_bytes = os.path.getsize(backup_path)
            
            if size_bytes < 1024:
                return f"{size_bytes} بايت"
            elif size_bytes < 1024 * 1024:
                return f"{size_bytes / 1024:.1f} كيلوبايت"
            else:
                return f"{size_bytes / (1024 * 1024):.1f} ميجابايت"
                
        except Exception:
            return "غير معروف"

    def restore_backup_dialog(self, parent):
        """نافذة حوار لاستعادة النسخة الاحتياطية"""
        try:
            from tkinter import filedialog, messagebox

            # اختيار ملف النسخة الاحتياطية
            backup_file = filedialog.askopenfilename(
                parent=parent,
                title="اختر ملف النسخة الاحتياطية",
                filetypes=[("Database files", "*.db"), ("All files", "*.*")],
                initialdir=self.backup_folder
            )

            if backup_file:
                # تأكيد الاستعادة
                if messagebox.askyesno("تأكيد الاستعادة",
                                     "هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\n\n"
                                     "تحذير: سيتم استبدال البيانات الحالية"):
                    self.restore_backup(backup_file)
                    messagebox.showinfo("نجح", "تم استعادة النسخة الاحتياطية بنجاح")
                    return True
            return False

        except Exception as e:
            from tkinter import messagebox
            messagebox.showerror("خطأ", f"فشل في استعادة النسخة الاحتياطية: {str(e)}")
            return False
