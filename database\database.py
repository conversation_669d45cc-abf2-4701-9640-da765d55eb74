# -*- coding: utf-8 -*-
"""
عمليات قاعدة البيانات لبرنامج محاسبة الصيدلية الزراعية
"""

from database.models import DatabaseManager
from datetime import datetime, date
from typing import List, Dict, Optional, Tuple

class PharmacyDatabase:
    """فئة إدارة عمليات قاعدة البيانات للصيدلية"""
    
    def __init__(self, db_path: str = "pharmacy_accounting.db"):
        self.db_manager = DatabaseManager(db_path)
    
    # ==================== عمليات المنتجات ====================
    
    def add_product(self, name: str, category: str, unit: str, 
                   purchase_price: float, selling_price: float,
                   stock_quantity: int = 0, min_stock_level: int = 10,
                   expiry_date: str = None, supplier_id: int = None) -> int:
        """إضافة منتج جديد"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO products (name, category, unit, purchase_price, 
                                selling_price, stock_quantity, min_stock_level,
                                expiry_date, supplier_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (name, category, unit, purchase_price, selling_price,
              stock_quantity, min_stock_level, expiry_date, supplier_id))
        
        product_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return product_id
    
    def get_all_products(self) -> List[Dict]:
        """الحصول على جميع المنتجات"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT p.*, s.name as supplier_name 
            FROM products p 
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            ORDER BY p.name
        ''')
        
        products = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return products

    def update_product(self, product_id: int, name: str = None, category: str = None,
                      unit: str = None, purchase_price: float = None,
                      selling_price: float = None, stock_quantity: int = None,
                      min_stock_level: int = None, expiry_date: str = None):
        """تحديث بيانات منتج"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        updates = []
        params = []

        if name is not None:
            updates.append("name = ?")
            params.append(name)
        if category is not None:
            updates.append("category = ?")
            params.append(category)
        if unit is not None:
            updates.append("unit = ?")
            params.append(unit)
        if purchase_price is not None:
            updates.append("purchase_price = ?")
            params.append(purchase_price)
        if selling_price is not None:
            updates.append("selling_price = ?")
            params.append(selling_price)
        if stock_quantity is not None:
            updates.append("stock_quantity = ?")
            params.append(stock_quantity)
        if min_stock_level is not None:
            updates.append("min_stock_level = ?")
            params.append(min_stock_level)
        if expiry_date is not None:
            updates.append("expiry_date = ?")
            params.append(expiry_date)

        if updates:
            updates.append("updated_at = CURRENT_TIMESTAMP")
            params.append(product_id)

            query = f"UPDATE products SET {', '.join(updates)} WHERE id = ?"
            cursor.execute(query, params)

        conn.commit()
        conn.close()

    def get_product_by_id(self, product_id: int) -> Dict:
        """الحصول على منتج بالمعرف"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM products WHERE id = ?', (product_id,))
        product = cursor.fetchone()
        conn.close()

        return dict(product) if product else None

    def delete_product(self, product_id: int):
        """حذف منتج"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        # التحقق من عدم وجود مبيعات مرتبطة
        cursor.execute('SELECT COUNT(*) FROM sale_items WHERE product_id = ?', (product_id,))
        sales_count = cursor.fetchone()[0]

        if sales_count > 0:
            raise ValueError("لا يمكن حذف المنتج لوجود مبيعات مرتبطة به")

        cursor.execute('DELETE FROM products WHERE id = ?', (product_id,))
        conn.commit()
        conn.close()

    def update_product_stock(self, product_id: int, quantity_change: int):
        """تحديث مخزون المنتج"""
        conn = None
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE products
                SET stock_quantity = stock_quantity + ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (quantity_change, product_id))

            conn.commit()
        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                conn.close()
    
    def get_low_stock_products(self) -> List[Dict]:
        """الحصول على المنتجات منخفضة المخزون"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM products 
            WHERE stock_quantity <= min_stock_level
            ORDER BY stock_quantity ASC
        ''')
        
        products = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return products
    
    # ==================== عمليات الزبائن ====================
    
    def add_customer(self, name: str, phone: str = None, address: str = None,
                    email: str = None, credit_limit: float = 0) -> int:
        """إضافة زبون جديد"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO customers (name, phone, address, email, credit_limit)
            VALUES (?, ?, ?, ?, ?)
        ''', (name, phone, address, email, credit_limit))
        
        customer_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return customer_id
    
    def get_all_customers(self) -> List[Dict]:
        """الحصول على جميع الزبائن"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM customers ORDER BY name')
        customers = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return customers
    
    def update_customer_debt(self, customer_id: int, debt_change: float):
        """تحديث دين الزبون"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE customers 
            SET current_debt = current_debt + ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (debt_change, customer_id))
        
        conn.commit()
        conn.close()
    
    # ==================== عمليات الموردين ====================
    
    def add_supplier(self, name: str, phone: str = None, 
                    address: str = None, email: str = None) -> int:
        """إضافة مورد جديد"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO suppliers (name, phone, address, email)
            VALUES (?, ?, ?, ?)
        ''', (name, phone, address, email))
        
        supplier_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return supplier_id
    
    def get_all_suppliers(self) -> List[Dict]:
        """الحصول على جميع الموردين"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM suppliers ORDER BY name')
        suppliers = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return suppliers

    # ==================== عمليات المبيعات ====================

    def add_sale(self, customer_id: int = None, total_amount: float = 0,
                paid_amount: float = 0, payment_method: str = 'نقدي',
                notes: str = None) -> int:
        """إضافة عملية بيع جديدة"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        remaining_amount = total_amount - paid_amount

        cursor.execute('''
            INSERT INTO sales (customer_id, total_amount, paid_amount,
                             remaining_amount, payment_method, notes)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (customer_id, total_amount, paid_amount, remaining_amount,
              payment_method, notes))

        sale_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return sale_id

    def add_sale_item(self, sale_id: int, product_id: int, quantity: int,
                     unit_price: float) -> int:
        """إضافة عنصر إلى عملية البيع"""
        conn = None
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            total_price = quantity * unit_price

            cursor.execute('''
                INSERT INTO sale_items (sale_id, product_id, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?)
            ''', (sale_id, product_id, quantity, unit_price, total_price))

            # تحديث المخزون في نفس الاتصال
            cursor.execute('''
                UPDATE products
                SET stock_quantity = stock_quantity + ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (-quantity, product_id))

            item_id = cursor.lastrowid
            conn.commit()
            return item_id

        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                conn.close()

    def get_all_sales(self, start_date: str = None, end_date: str = None) -> List[Dict]:
        """الحصول على جميع المبيعات"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        query = '''
            SELECT s.*, c.name as customer_name
            FROM sales s
            LEFT JOIN customers c ON s.customer_id = c.id
        '''
        params = []

        if start_date and end_date:
            query += ' WHERE s.sale_date BETWEEN ? AND ?'
            params.extend([start_date, end_date])
        elif start_date:
            query += ' WHERE s.sale_date >= ?'
            params.append(start_date)
        elif end_date:
            query += ' WHERE s.sale_date <= ?'
            params.append(end_date)

        query += ' ORDER BY s.created_at DESC'

        cursor.execute(query, params)
        sales = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return sales

    def get_sale_items(self, sale_id: int) -> List[Dict]:
        """الحصول على عناصر عملية بيع معينة"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT si.*, p.name as product_name, p.unit
            FROM sale_items si
            JOIN products p ON si.product_id = p.id
            WHERE si.sale_id = ?
        ''', (sale_id,))

        items = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return items

    def get_daily_sales_summary(self, date: str = None) -> Dict:
        """الحصول على ملخص المبيعات اليومية"""
        if not date:
            date = datetime.now().strftime('%Y-%m-%d')

        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT
                COUNT(*) as sales_count,
                SUM(total_amount) as total_sales,
                SUM(paid_amount) as total_paid,
                SUM(remaining_amount) as total_remaining
            FROM sales
            WHERE DATE(sale_date) = ?
        ''', (date,))

        result = dict(cursor.fetchone())
        conn.close()

        # تعيين قيم افتراضية إذا لم توجد مبيعات
        for key in result:
            if result[key] is None:
                result[key] = 0

        return result

    # ==================== عمليات المدفوعات ====================

    def add_payment(self, customer_id: int = None, supplier_id: int = None,
                   amount: float = 0, payment_type: str = 'دفع',
                   payment_method: str = 'نقدي', notes: str = None) -> int:
        """إضافة دفعة جديدة"""
        conn = None
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO payments (customer_id, supplier_id, amount, payment_type,
                                    payment_method, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (customer_id, supplier_id, amount, payment_type, payment_method, notes))

            payment_id = cursor.lastrowid

            # تحديث دين الزبون إذا كان الدفع من زبون في نفس الاتصال
            if customer_id and payment_type == 'استلام':
                cursor.execute('''
                    UPDATE customers
                    SET current_debt = current_debt + ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (-amount, customer_id))
            elif customer_id and payment_type == 'دفع':
                cursor.execute('''
                    UPDATE customers
                    SET current_debt = current_debt + ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (amount, customer_id))

            conn.commit()
            return payment_id

        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                conn.close()

    def get_customer_payments(self, customer_id: int) -> List[Dict]:
        """الحصول على مدفوعات زبون معين"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM payments
            WHERE customer_id = ?
            ORDER BY created_at DESC
        ''', (customer_id,))

        payments = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return payments

    # ==================== عمليات التقارير ====================

    def get_customers_with_debts(self) -> List[Dict]:
        """الحصول على الزبائن الذين لديهم ديون"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM customers
            WHERE current_debt > 0
            ORDER BY current_debt DESC
        ''')

        customers = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return customers

    def get_monthly_sales_report(self, year: int, month: int) -> Dict:
        """تقرير المبيعات الشهرية"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        # تحديد بداية ونهاية الشهر
        start_date = f"{year}-{month:02d}-01"
        if month == 12:
            end_date = f"{year + 1}-01-01"
        else:
            end_date = f"{year}-{month + 1:02d}-01"

        cursor.execute('''
            SELECT
                COUNT(*) as sales_count,
                SUM(total_amount) as total_sales,
                SUM(paid_amount) as total_paid,
                SUM(remaining_amount) as total_remaining,
                AVG(total_amount) as average_sale
            FROM sales
            WHERE sale_date >= ? AND sale_date < ?
        ''', (start_date, end_date))

        result = dict(cursor.fetchone())
        conn.close()

        # تعيين قيم افتراضية
        for key in result:
            if result[key] is None:
                result[key] = 0

        return result

    def get_top_selling_products(self, limit: int = 10) -> List[Dict]:
        """الحصول على أكثر المنتجات مبيعاً"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT
                p.id, p.name, p.category, p.unit,
                SUM(si.quantity) as total_sold,
                SUM(si.total_price) as total_revenue
            FROM products p
            JOIN sale_items si ON p.id = si.product_id
            GROUP BY p.id, p.name, p.category, p.unit
            ORDER BY total_sold DESC
            LIMIT ?
        ''', (limit,))

        products = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return products

    def get_profit_report(self, start_date: str = None, end_date: str = None) -> Dict:
        """تقرير الأرباح"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        query = '''
            SELECT
                SUM(si.total_price) as total_revenue,
                SUM(si.quantity * p.purchase_price) as total_cost,
                SUM(si.total_price - (si.quantity * p.purchase_price)) as total_profit
            FROM sale_items si
            JOIN products p ON si.product_id = p.id
            JOIN sales s ON si.sale_id = s.id
        '''
        params = []

        if start_date and end_date:
            query += ' WHERE s.sale_date BETWEEN ? AND ?'
            params.extend([start_date, end_date])
        elif start_date:
            query += ' WHERE s.sale_date >= ?'
            params.append(start_date)
        elif end_date:
            query += ' WHERE s.sale_date <= ?'
            params.append(end_date)

        cursor.execute(query, params)
        result = dict(cursor.fetchone())
        conn.close()

        # تعيين قيم افتراضية
        for key in result:
            if result[key] is None:
                result[key] = 0

        # حساب هامش الربح
        if result['total_revenue'] > 0:
            result['profit_margin'] = (result['total_profit'] / result['total_revenue']) * 100
        else:
            result['profit_margin'] = 0

        return result

    # ==================== عمليات إضافية ====================

    def update_customer(self, customer_id: int, name: str = None, phone: str = None,
                       address: str = None, email: str = None, credit_limit: float = None):
        """تحديث بيانات زبون"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        updates = []
        params = []

        if name is not None:
            updates.append("name = ?")
            params.append(name)
        if phone is not None:
            updates.append("phone = ?")
            params.append(phone)
        if address is not None:
            updates.append("address = ?")
            params.append(address)
        if email is not None:
            updates.append("email = ?")
            params.append(email)
        if credit_limit is not None:
            updates.append("credit_limit = ?")
            params.append(credit_limit)

        if updates:
            updates.append("updated_at = CURRENT_TIMESTAMP")
            params.append(customer_id)

            query = f"UPDATE customers SET {', '.join(updates)} WHERE id = ?"
            cursor.execute(query, params)

        conn.commit()
        conn.close()

    def update_supplier(self, supplier_id: int, name: str = None, phone: str = None,
                       address: str = None, email: str = None):
        """تحديث بيانات مورد"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        updates = []
        params = []

        if name is not None:
            updates.append("name = ?")
            params.append(name)
        if phone is not None:
            updates.append("phone = ?")
            params.append(phone)
        if address is not None:
            updates.append("address = ?")
            params.append(address)
        if email is not None:
            updates.append("email = ?")
            params.append(email)

        if updates:
            updates.append("updated_at = CURRENT_TIMESTAMP")
            params.append(supplier_id)

            query = f"UPDATE suppliers SET {', '.join(updates)} WHERE id = ?"
            cursor.execute(query, params)

        conn.commit()
        conn.close()

    def delete_customer(self, customer_id: int):
        """حذف زبون"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        # التحقق من عدم وجود مبيعات مرتبطة
        cursor.execute('SELECT COUNT(*) FROM sales WHERE customer_id = ?', (customer_id,))
        sales_count = cursor.fetchone()[0]

        if sales_count > 0:
            raise ValueError("لا يمكن حذف الزبون لوجود مبيعات مرتبطة به")

        cursor.execute('DELETE FROM customers WHERE id = ?', (customer_id,))
        conn.commit()
        conn.close()

    def delete_supplier(self, supplier_id: int):
        """حذف مورد"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        # التحقق من عدم وجود منتجات مرتبطة
        cursor.execute('SELECT COUNT(*) FROM products WHERE supplier_id = ?', (supplier_id,))
        products_count = cursor.fetchone()[0]

        if products_count > 0:
            raise ValueError("لا يمكن حذف المورد لوجود منتجات مرتبطة به")

        cursor.execute('DELETE FROM suppliers WHERE id = ?', (supplier_id,))
        conn.commit()
        conn.close()

    def get_customer_by_id(self, customer_id: int) -> Dict:
        """الحصول على زبون بالمعرف"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM customers WHERE id = ?', (customer_id,))
        customer = cursor.fetchone()
        conn.close()

        return dict(customer) if customer else None

    def get_supplier_by_id(self, supplier_id: int) -> Dict:
        """الحصول على مورد بالمعرف"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM suppliers WHERE id = ?', (supplier_id,))
        supplier = cursor.fetchone()
        conn.close()

        return dict(supplier) if supplier else None

    # ==================== دوال المشتريات ====================

    def add_purchase(self, supplier_id: int, purchase_date: str, total_amount: float,
                    paid_amount: float = 0, notes: str = "") -> int:
        """إضافة مشترى جديد"""
        conn = None
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            remaining_amount = total_amount - paid_amount

            cursor.execute('''
                INSERT INTO purchases (supplier_id, purchase_date, total_amount, paid_amount, remaining_amount, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (supplier_id, purchase_date, total_amount, paid_amount, remaining_amount, notes))

            purchase_id = cursor.lastrowid
            conn.commit()
            return purchase_id

        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                conn.close()

    def add_purchase_item(self, purchase_id: int, product_id: int, quantity: int, unit_price: float) -> int:
        """إضافة منتج للمشترى"""
        conn = None
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            total_price = quantity * unit_price

            cursor.execute('''
                INSERT INTO purchase_items (purchase_id, product_id, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?)
            ''', (purchase_id, product_id, quantity, unit_price, total_price))

            item_id = cursor.lastrowid

            # تحديث مخزون المنتج
            self.update_product_stock(product_id, quantity)

            conn.commit()
            return item_id

        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                conn.close()

    def get_all_purchases(self) -> List[Dict]:
        """الحصول على جميع المشتريات"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT p.*, s.name as supplier_name
            FROM purchases p
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            ORDER BY p.purchase_date DESC
        ''')

        purchases = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return purchases

    def get_purchase_items(self, purchase_id: int) -> List[Dict]:
        """الحصول على منتجات المشترى"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT pi.*, p.name as product_name, p.unit
            FROM purchase_items pi
            LEFT JOIN products p ON pi.product_id = p.id
            WHERE pi.purchase_id = ?
        ''', (purchase_id,))

        items = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return items

    def update_purchase_payment(self, purchase_id: int, payment_amount: float):
        """تحديث دفعة المشترى"""
        conn = None
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE purchases
                SET paid_amount = paid_amount + ?,
                    remaining_amount = remaining_amount - ?
                WHERE id = ?
            ''', (payment_amount, payment_amount, purchase_id))

            conn.commit()

        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                conn.close()

    def get_supplier_debt(self, supplier_id: int) -> float:
        """الحصول على دين المورد"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT COALESCE(SUM(remaining_amount), 0)
            FROM purchases
            WHERE supplier_id = ? AND remaining_amount > 0
        ''', (supplier_id,))

        debt = cursor.fetchone()[0]
        conn.close()
        return debt

    def get_customer_debt(self, customer_id: int) -> float:
        """الحصول على دين الزبون من جدول الزبائن"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT COALESCE(current_debt, 0)
            FROM customers
            WHERE id = ?
        ''', (customer_id,))

        result = cursor.fetchone()
        debt = result[0] if result else 0
        conn.close()
        return debt
