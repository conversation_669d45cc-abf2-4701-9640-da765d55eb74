# -*- coding: utf-8 -*-
"""
واجهة المبيعات لبرنامج محاسبة الصيدلية الزراعية
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime
import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import *
from database.database import PharmacyDatabase

class SalesWindow:
    """نافذة عرض المبيعات"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.db = PharmacyDatabase()
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.setup_window()
        self.create_interface()
        self.load_sales()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة المبيعات")
        self.window.geometry("1200x700")
        self.window.configure(bg=COLORS['background'])
        self.center_window()
        
    def center_window(self):
        """وضع النافذة في منتصف الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg=COLORS['success'], height=60)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="إدارة المبيعات",
                              font=(FONT_FAMILY, 18, 'bold'),
                              bg=COLORS['success'], fg='white')
        title_label.pack(expand=True)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # أزرار العمليات
        tk.Button(buttons_frame, text="بيع جديد",
                 command=self.new_sale,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['success'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="عرض تفاصيل البيع",
                 command=self.view_sale_details,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['primary'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تقرير يومي",
                 command=self.daily_report,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['secondary'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تحديث",
                 command=self.load_sales,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg='#6c757d', fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        # إطار التصفية
        filter_frame = tk.Frame(self.window, bg=COLORS['background'])
        filter_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(filter_frame, text="من تاريخ:",
                font=(FONT_FAMILY, 12),
                bg=COLORS['background']).pack(side=tk.LEFT, padx=5)
        
        self.start_date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        start_date_entry = tk.Entry(filter_frame, textvariable=self.start_date_var,
                                   font=(FONT_FAMILY, 12), width=12)
        start_date_entry.pack(side=tk.LEFT, padx=5)
        
        tk.Label(filter_frame, text="إلى تاريخ:",
                font=(FONT_FAMILY, 12),
                bg=COLORS['background']).pack(side=tk.LEFT, padx=5)
        
        self.end_date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        end_date_entry = tk.Entry(filter_frame, textvariable=self.end_date_var,
                                 font=(FONT_FAMILY, 12), width=12)
        end_date_entry.pack(side=tk.LEFT, padx=5)
        
        tk.Button(filter_frame, text="تصفية",
                 command=self.filter_sales,
                 font=(FONT_FAMILY, 12),
                 bg=COLORS['primary'], fg='white',
                 width=10).pack(side=tk.LEFT, padx=10)
        
        # إطار الجدول
        table_frame = tk.Frame(self.window, bg=COLORS['background'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # إنشاء الجدول
        self.create_sales_table(table_frame)
        
        # إطار المعلومات
        info_frame = tk.Frame(self.window, bg=COLORS['background'])
        info_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.info_label = tk.Label(info_frame, text="",
                                  font=(FONT_FAMILY, 12, 'bold'),
                                  bg=COLORS['background'])
        self.info_label.pack(anchor=tk.W)
        
    def create_sales_table(self, parent):
        """إنشاء جدول المبيعات"""
        columns = ('id', 'customer_name', 'total_amount', 'paid_amount', 
                  'remaining_amount', 'payment_method', 'sale_date')
        
        self.tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة
        headers = {
            'id': 'رقم البيع',
            'customer_name': 'الزبون',
            'total_amount': 'المبلغ الإجمالي',
            'paid_amount': 'المبلغ المدفوع',
            'remaining_amount': 'المبلغ المتبقي',
            'payment_method': 'طريقة الدفع',
            'sale_date': 'تاريخ البيع'
        }
        
        for col in columns:
            self.tree.heading(col, text=headers[col])
            if col == 'id':
                self.tree.column(col, width=80, anchor=tk.CENTER)
            elif col in ['total_amount', 'paid_amount', 'remaining_amount']:
                self.tree.column(col, width=120, anchor=tk.CENTER)
            elif col in ['payment_method', 'sale_date']:
                self.tree.column(col, width=120, anchor=tk.CENTER)
            else:
                self.tree.column(col, width=150, anchor=tk.CENTER)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.tree.bind('<Double-1>', self.on_item_double_click)
        
    def load_sales(self):
        """تحميل المبيعات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل المبيعات من قاعدة البيانات
            sales = self.db.get_all_sales()
            
            total_sales = 0
            total_paid = 0
            total_remaining = 0
            
            for sale in sales:
                # تلوين الصفوف حسب حالة الدفع
                tags = []
                if sale['remaining_amount'] > 0:
                    tags.append('has_debt')
                
                self.tree.insert('', tk.END, values=(
                    sale['id'],
                    sale['customer_name'] or 'زبون نقدي',
                    f"{sale['total_amount']:.2f}",
                    f"{sale['paid_amount']:.2f}",
                    f"{sale['remaining_amount']:.2f}",
                    sale['payment_method'],
                    sale['sale_date']
                ), tags=tags)
                
                total_sales += sale['total_amount']
                total_paid += sale['paid_amount']
                total_remaining += sale['remaining_amount']
            
            # تعيين ألوان للصفوف
            self.tree.tag_configure('has_debt', background='#fff3cd')
            
            # تحديث معلومات الإحصائيات
            self.update_info_label(len(sales), total_sales, total_paid, total_remaining)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المبيعات: {str(e)}")
    
    def update_info_label(self, count, total_sales, total_paid, total_remaining):
        """تحديث تسمية المعلومات"""
        info_text = (f"عدد المبيعات: {count} | "
                    f"إجمالي المبيعات: {total_sales:.2f} {CURRENCY_SYMBOL} | "
                    f"المدفوع: {total_paid:.2f} {CURRENCY_SYMBOL} | "
                    f"المتبقي: {total_remaining:.2f} {CURRENCY_SYMBOL}")
        self.info_label.config(text=info_text)
    
    def filter_sales(self):
        """تصفية المبيعات حسب التاريخ"""
        try:
            start_date = self.start_date_var.get()
            end_date = self.end_date_var.get()
            
            # التحقق من صحة التواريخ
            datetime.strptime(start_date, '%Y-%m-%d')
            datetime.strptime(end_date, '%Y-%m-%d')
            
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل المبيعات المصفاة
            sales = self.db.get_all_sales(start_date, end_date)
            
            total_sales = 0
            total_paid = 0
            total_remaining = 0
            
            for sale in sales:
                tags = []
                if sale['remaining_amount'] > 0:
                    tags.append('has_debt')
                
                self.tree.insert('', tk.END, values=(
                    sale['id'],
                    sale['customer_name'] or 'زبون نقدي',
                    f"{sale['total_amount']:.2f}",
                    f"{sale['paid_amount']:.2f}",
                    f"{sale['remaining_amount']:.2f}",
                    sale['payment_method'],
                    sale['sale_date']
                ), tags=tags)
                
                total_sales += sale['total_amount']
                total_paid += sale['paid_amount']
                total_remaining += sale['remaining_amount']
            
            # تحديث المعلومات
            self.update_info_label(len(sales), total_sales, total_paid, total_remaining)
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال تواريخ صحيحة بصيغة YYYY-MM-DD")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصفية المبيعات: {str(e)}")
    
    def new_sale(self):
        """فتح نافذة بيع جديد"""
        try:
            NewSaleWindow(self.window, self.db, self.load_sales)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة البيع: {str(e)}")
    
    def view_sale_details(self):
        """عرض تفاصيل البيع المحدد"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار بيع لعرض تفاصيله")
            return
        
        # الحصول على رقم البيع
        item_values = self.tree.item(selected_item[0])['values']
        sale_id = item_values[0]
        
        try:
            # الحصول على تفاصيل البيع
            sale_items = self.db.get_sale_items(sale_id)
            
            if not sale_items:
                messagebox.showinfo("معلومة", "لا توجد تفاصيل لهذا البيع")
                return
            
            # إنشاء نافذة عرض التفاصيل
            SaleDetailsWindow(self.window, sale_id, sale_items)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل تفاصيل البيع: {str(e)}")
    
    def daily_report(self):
        """عرض التقرير اليومي"""
        try:
            today = datetime.now().strftime('%Y-%m-%d')
            summary = self.db.get_daily_sales_summary(today)
            
            report_text = f"""
تقرير المبيعات اليومية - {today}

عدد المبيعات: {summary['sales_count']}
إجمالي المبيعات: {summary['total_sales']:.2f} {CURRENCY_SYMBOL}
المبلغ المدفوع: {summary['total_paid']:.2f} {CURRENCY_SYMBOL}
المبلغ المتبقي: {summary['total_remaining']:.2f} {CURRENCY_SYMBOL}
            """
            
            messagebox.showinfo("التقرير اليومي", report_text)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير: {str(e)}")
    
    def on_item_double_click(self, event):
        """التعامل مع النقر المزدوج على عنصر"""
        self.view_sale_details()

class NewSaleWindow:
    """نافذة بيع جديد"""

    def __init__(self, parent, db, refresh_callback=None):
        self.parent = parent
        self.db = db
        self.refresh_callback = refresh_callback
        self.window = tk.Toplevel(parent)
        self.sale_items = []  # قائمة عناصر البيع
        self.setup_window()
        self.create_interface()
        self.load_products()
        self.load_customers()

    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("بيع جديد")
        self.window.geometry("1000x700")
        self.window.configure(bg=COLORS['background'])
        self.window.transient(self.parent)
        self.center_window()

    def center_window(self):
        """وضع النافذة في المنتصف"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg=COLORS['success'], height=50)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="بيع جديد",
                              font=(FONT_FAMILY, 16, 'bold'),
                              bg=COLORS['success'], fg='white')
        title_label.pack(expand=True)

        # إطار معلومات البيع
        info_frame = tk.Frame(self.window, bg=COLORS['background'])
        info_frame.pack(fill=tk.X, padx=10, pady=5)

        # اختيار الزبون
        tk.Label(info_frame, text="الزبون:",
                font=(FONT_FAMILY, 12),
                bg=COLORS['background']).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        self.customer_var = tk.StringVar()
        self.customer_combo = ttk.Combobox(info_frame, textvariable=self.customer_var,
                                          font=(FONT_FAMILY, 12), width=25, state='readonly')
        self.customer_combo.grid(row=0, column=1, padx=5, pady=5)

        # طريقة الدفع
        tk.Label(info_frame, text="طريقة الدفع:",
                font=(FONT_FAMILY, 12),
                bg=COLORS['background']).grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)

        self.payment_method_var = tk.StringVar(value='نقدي')
        payment_combo = ttk.Combobox(info_frame, textvariable=self.payment_method_var,
                                    values=PAYMENT_METHODS, font=(FONT_FAMILY, 12),
                                    width=15, state='readonly')
        payment_combo.grid(row=0, column=3, padx=5, pady=5)

        # إطار إضافة المنتجات
        add_frame = tk.Frame(self.window, bg=COLORS['background'])
        add_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(add_frame, text="المنتج:",
                font=(FONT_FAMILY, 12),
                bg=COLORS['background']).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        self.product_var = tk.StringVar()
        self.product_combo = ttk.Combobox(add_frame, textvariable=self.product_var,
                                         font=(FONT_FAMILY, 12), width=25, state='readonly')
        self.product_combo.grid(row=0, column=1, padx=5, pady=5)
        self.product_combo.bind('<<ComboboxSelected>>', self.on_product_selected)

        tk.Label(add_frame, text="الكمية:",
                font=(FONT_FAMILY, 12),
                bg=COLORS['background']).grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)

        self.quantity_var = tk.StringVar(value='1')
        quantity_entry = tk.Entry(add_frame, textvariable=self.quantity_var,
                                 font=(FONT_FAMILY, 12), width=10)
        quantity_entry.grid(row=0, column=3, padx=5, pady=5)

        tk.Label(add_frame, text="السعر:",
                font=(FONT_FAMILY, 12),
                bg=COLORS['background']).grid(row=0, column=4, sticky=tk.W, padx=5, pady=5)

        self.price_var = tk.StringVar()
        self.price_entry = tk.Entry(add_frame, textvariable=self.price_var,
                                   font=(FONT_FAMILY, 12), width=10)
        self.price_entry.grid(row=0, column=5, padx=5, pady=5)

        tk.Button(add_frame, text="إضافة",
                 command=self.add_item,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['primary'], fg='white',
                 width=10).grid(row=0, column=6, padx=10, pady=5)

        # إطار جدول العناصر
        items_frame = tk.Frame(self.window, bg=COLORS['background'])
        items_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # إنشاء جدول العناصر
        self.create_items_table(items_frame)

        # إطار الإجماليات
        totals_frame = tk.Frame(self.window, bg=COLORS['background'])
        totals_frame.pack(fill=tk.X, padx=10, pady=5)

        self.total_label = tk.Label(totals_frame, text="الإجمالي: 0.00 ريال",
                                   font=(FONT_FAMILY, 14, 'bold'),
                                   bg=COLORS['background'])
        self.total_label.pack(side=tk.RIGHT, padx=10)

        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Button(buttons_frame, text="حفظ البيع",
                 command=self.save_sale,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['success'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="إلغاء",
                 command=self.window.destroy,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['warning'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="حذف عنصر",
                 command=self.remove_item,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['warning'], fg='white',
                 width=15).pack(side=tk.RIGHT, padx=5)

    def create_items_table(self, parent):
        """إنشاء جدول عناصر البيع"""
        columns = ('product_name', 'quantity', 'unit_price', 'total_price')

        self.items_tree = ttk.Treeview(parent, columns=columns, show='headings', height=10)

        # تعيين عناوين الأعمدة
        headers = {
            'product_name': 'المنتج',
            'quantity': 'الكمية',
            'unit_price': 'سعر الوحدة',
            'total_price': 'الإجمالي'
        }

        for col in columns:
            self.items_tree.heading(col, text=headers[col])
            if col == 'product_name':
                self.items_tree.column(col, width=300, anchor=tk.W)
            else:
                self.items_tree.column(col, width=120, anchor=tk.CENTER)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.items_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def load_products(self):
        """تحميل المنتجات في القائمة المنسدلة"""
        try:
            products = self.db.get_all_products()
            product_list = []
            self.products_dict = {}

            for product in products:
                if product['stock_quantity'] > 0:  # فقط المنتجات المتوفرة
                    display_name = f"{product['name']} ({product['stock_quantity']} {product['unit']})"
                    product_list.append(display_name)
                    self.products_dict[display_name] = product

            self.product_combo['values'] = product_list

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المنتجات: {str(e)}")

    def load_customers(self):
        """تحميل الزبائن في القائمة المنسدلة"""
        try:
            customers = self.db.get_all_customers()
            customer_list = ['زبون نقدي']  # خيار افتراضي
            self.customers_dict = {'زبون نقدي': None}

            for customer in customers:
                customer_list.append(customer['name'])
                self.customers_dict[customer['name']] = customer

            self.customer_combo['values'] = customer_list
            self.customer_combo.set('زبون نقدي')

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الزبائن: {str(e)}")

    def on_product_selected(self, event):
        """عند اختيار منتج"""
        selected_product = self.product_var.get()
        if selected_product in self.products_dict:
            product = self.products_dict[selected_product]
            self.price_var.set(str(product['selling_price']))

    def add_item(self):
        """إضافة عنصر إلى البيع"""
        try:
            # التحقق من البيانات
            selected_product = self.product_var.get()
            if not selected_product or selected_product not in self.products_dict:
                raise ValueError("يرجى اختيار منتج")

            quantity = int(self.quantity_var.get())
            if quantity <= 0:
                raise ValueError("الكمية يجب أن تكون أكبر من صفر")

            unit_price = float(self.price_var.get())
            if unit_price <= 0:
                raise ValueError("السعر يجب أن يكون أكبر من صفر")

            product = self.products_dict[selected_product]

            # التحقق من توفر الكمية
            if quantity > product['stock_quantity']:
                raise ValueError(f"الكمية المطلوبة ({quantity}) أكبر من المتوفر ({product['stock_quantity']})")

            # إضافة العنصر
            total_price = quantity * unit_price

            item = {
                'product_id': product['id'],
                'product_name': product['name'],
                'quantity': quantity,
                'unit_price': unit_price,
                'total_price': total_price
            }

            self.sale_items.append(item)

            # إضافة إلى الجدول
            self.items_tree.insert('', tk.END, values=(
                product['name'],
                quantity,
                f"{unit_price:.2f}",
                f"{total_price:.2f}"
            ))

            # تحديث الإجمالي
            self.update_total()

            # مسح الحقول
            self.product_var.set('')
            self.quantity_var.set('1')
            self.price_var.set('')

        except ValueError as e:
            messagebox.showerror("خطأ", str(e))
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إضافة العنصر: {str(e)}")

    def remove_item(self):
        """حذف عنصر من البيع"""
        selected_item = self.items_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار عنصر للحذف")
            return

        # الحصول على فهرس العنصر
        item_index = self.items_tree.index(selected_item[0])

        # حذف من القائمة والجدول
        del self.sale_items[item_index]
        self.items_tree.delete(selected_item[0])

        # تحديث الإجمالي
        self.update_total()

    def update_total(self):
        """تحديث الإجمالي"""
        total = sum(item['total_price'] for item in self.sale_items)
        self.total_label.config(text=f"الإجمالي: {total:.2f} {CURRENCY_SYMBOL}")

    def save_sale(self):
        """حفظ البيع"""
        try:
            if not self.sale_items:
                raise ValueError("يرجى إضافة منتجات للبيع")

            # حساب الإجمالي
            total_amount = sum(item['total_price'] for item in self.sale_items)

            # الحصول على معرف الزبون
            customer_name = self.customer_var.get()
            customer_id = None
            if customer_name != 'زبون نقدي' and customer_name in self.customers_dict:
                customer_id = self.customers_dict[customer_name]['id']

            # طلب المبلغ المدفوع
            paid_amount = simpledialog.askfloat(
                "المبلغ المدفوع",
                f"الإجمالي: {total_amount:.2f} {CURRENCY_SYMBOL}\n\nأدخل المبلغ المدفوع:",
                minvalue=0,
                maxvalue=total_amount if customer_id is None else None  # للزبائن النقديين فقط
            )

            if paid_amount is None:
                return  # المستخدم ألغى العملية

            # إنشاء البيع
            sale_id = self.db.add_sale(
                customer_id=customer_id,
                total_amount=total_amount,
                paid_amount=paid_amount,
                payment_method=self.payment_method_var.get(),
                notes=f"بيع تم في {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )

            # إضافة عناصر البيع
            for item in self.sale_items:
                self.db.add_sale_item(
                    sale_id=sale_id,
                    product_id=item['product_id'],
                    quantity=item['quantity'],
                    unit_price=item['unit_price']
                )

            # تحديث دين الزبون إذا كان هناك مبلغ متبقي
            remaining_amount = total_amount - paid_amount
            if customer_id and remaining_amount > 0:
                self.db.update_customer_debt(customer_id, remaining_amount)

            messagebox.showinfo("نجح", f"تم حفظ البيع برقم {sale_id} بنجاح")

            # تحديث قائمة المبيعات
            if self.refresh_callback:
                self.refresh_callback()

            # إغلاق النافذة
            self.window.destroy()

        except ValueError as e:
            messagebox.showerror("خطأ", str(e))
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيع: {str(e)}")

class SaleDetailsWindow:
    """نافذة عرض تفاصيل البيع"""

    def __init__(self, parent, sale_id, sale_items):
        self.window = tk.Toplevel(parent)
        self.window.title(f"تفاصيل البيع رقم: {sale_id}")
        self.window.geometry("800x500")
        self.window.configure(bg=COLORS['background'])
        self.window.transient(parent)

        self.sale_items = sale_items
        self.create_interface()
        self.load_items()
        self.center_window()

    def center_window(self):
        """وضع النافذة في المنتصف"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def create_interface(self):
        """إنشاء واجهة عرض التفاصيل"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg=COLORS['primary'], height=50)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="تفاصيل البيع",
                              font=(FONT_FAMILY, 16, 'bold'),
                              bg=COLORS['primary'], fg='white')
        title_label.pack(expand=True)

        # إطار الجدول
        table_frame = tk.Frame(self.window, bg=COLORS['background'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # إنشاء جدول التفاصيل
        columns = ('product_name', 'unit', 'quantity', 'unit_price', 'total_price')

        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # تعيين عناوين الأعمدة
        headers = {
            'product_name': 'المنتج',
            'unit': 'الوحدة',
            'quantity': 'الكمية',
            'unit_price': 'سعر الوحدة',
            'total_price': 'الإجمالي'
        }

        for col in columns:
            self.tree.heading(col, text=headers[col])
            if col == 'product_name':
                self.tree.column(col, width=250, anchor=tk.W)
            elif col == 'unit':
                self.tree.column(col, width=80, anchor=tk.CENTER)
            else:
                self.tree.column(col, width=120, anchor=tk.CENTER)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # إطار الملخص
        summary_frame = tk.Frame(self.window, bg=COLORS['background'])
        summary_frame.pack(fill=tk.X, padx=10, pady=5)

        self.summary_label = tk.Label(summary_frame, text="",
                                     font=(FONT_FAMILY, 12, 'bold'),
                                     bg=COLORS['background'])
        self.summary_label.pack()

        # زر الإغلاق
        close_button = tk.Button(self.window, text="إغلاق",
                               command=self.window.destroy,
                               font=(FONT_FAMILY, 12, 'bold'),
                               bg=COLORS['warning'], fg='white',
                               width=15)
        close_button.pack(pady=10)

    def load_items(self):
        """تحميل عناصر البيع في الجدول"""
        total_amount = 0
        total_quantity = 0

        for item in self.sale_items:
            self.tree.insert('', tk.END, values=(
                item['product_name'],
                item['unit'],
                item['quantity'],
                f"{item['unit_price']:.2f}",
                f"{item['total_price']:.2f}"
            ))

            total_amount += item['total_price']
            total_quantity += item['quantity']

        # تحديث الملخص
        self.summary_label.config(
            text=f"إجمالي العناصر: {len(self.sale_items)} | إجمالي الكمية: {total_quantity} | المبلغ الإجمالي: {total_amount:.2f} {CURRENCY_SYMBOL}"
        )

if __name__ == "__main__":
    # اختبار النافذة
    app = SalesWindow()
    app.window.mainloop()
