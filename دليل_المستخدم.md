# دليل المستخدم - برنامج محاسبة الصيدلية الزراعية

## البدء السريع

### 1. التثبيت الأولي
1. تأكد من تثبيت Python 3.7 أو أحدث
2. شغ<PERSON> ملف `install.bat` لتثبيت المتطلبات
3. شغ<PERSON> ملف `run.bat` لتشغيل البرنامج

### 2. إضافة البيانات التجريبية (اختياري)
- شغل `python add_sample_data.py` لإضافة بيانات تجريبية
- هذا سيساعدك على فهم كيفية عمل البرنامج

## الواجهة الرئيسية

عند تشغيل البرنامج، ستظهر لك الواجهة الرئيسية التي تحتوي على:

### الأزرار الرئيسية:
- **بيع جديد**: لتسجيل عملية بيع جديدة (قيد التطوير)
- **إدارة المنتجات**: لإدارة المخزون والمنتجات ✅
- **إدارة الزبائن**: لإدارة بيانات الزبائن (قيد التطوير)
- **إدارة الموردين**: لإدارة بيانات الموردين (قيد التطوير)
- **تقارير المبيعات**: لعرض تقارير المبيعات (قيد التطوير)
- **تقرير المخزون**: لعرض تقرير المخزون (قيد التطوير)

### شريط القوائم:
- **ملف**: النسخ الاحتياطية والخروج
- **المبيعات**: عمليات البيع والمبيعات
- **المخزون**: إدارة المنتجات والمخزون
- **الزبائن**: إدارة الزبائن والديون
- **الموردين**: إدارة الموردين
- **التقارير**: التقارير المالية والإحصائية
- **مساعدة**: معلومات حول البرنامج

## إدارة المنتجات والمخزون

### فتح نافذة إدارة المنتجات:
1. اضغط على "إدارة المنتجات" من الواجهة الرئيسية
2. أو من القائمة: المخزون → إدارة المنتجات

### إضافة منتج جديد:
1. اضغط "إضافة منتج جديد"
2. املأ البيانات المطلوبة:
   - **اسم المنتج**: اسم واضح ومميز
   - **الفئة**: اختر من القائمة أو أدخل فئة جديدة
   - **الوحدة**: وحدة القياس (كيلو، لتر، قطعة، إلخ)
   - **سعر الشراء**: السعر الذي اشتريت به المنتج
   - **سعر البيع**: السعر الذي ستبيع به المنتج
   - **الكمية الحالية**: الكمية الموجودة في المخزون
   - **الحد الأدنى**: الحد الأدنى للتنبيه عند نقص المخزون
3. اضغط "حفظ"

### البحث في المنتجات:
- استخدم مربع البحث لإيجاد منتج معين
- يمكن البحث بالاسم أو الفئة أو اسم المورد

### تحديث المخزون:
1. اختر المنتج من الجدول
2. اضغط "تحديث المخزون"
3. أدخل الكمية الجديدة
4. اضغط "موافق"

### فهم ألوان الجدول:
- **أبيض**: مخزون طبيعي
- **وردي فاتح**: مخزون منخفض (أقل من الحد الأدنى)
- **وردي غامق**: نفد من المخزون (كمية = 0)

## النسخ الاحتياطية

### النسخ التلقائية:
- يتم إنشاء نسخة احتياطية تلقائياً كل 7 أيام
- النسخ محفوظة في مجلد `backups`

### إنشاء نسخة احتياطية يدوياً:
1. من القائمة: ملف → نسخة احتياطية
2. سيتم حفظ النسخة في مجلد `backups`

### استعادة نسخة احتياطية:
1. من القائمة: ملف → استعادة نسخة احتياطية
2. اختر الملف المطلوب
3. تأكيد الاستعادة

## نصائح مهمة

### للحصول على أفضل أداء:
1. **أغلق البرنامج بشكل صحيح** باستخدام زر الخروج
2. **لا تحذف ملف قاعدة البيانات** `pharmacy_accounting.db`
3. **احتفظ بنسخ احتياطية منتظمة** من بياناتك

### في حالة حدوث مشاكل:
1. **تأكد من تثبيت Python بشكل صحيح**
2. **شغل `install.bat` مرة أخرى** لتثبيت المتطلبات
3. **تحقق من وجود ملف قاعدة البيانات**
4. **استعد نسخة احتياطية** إذا لزم الأمر

## الفئات الافتراضية للمنتجات

- مبيدات حشرية
- مبيدات فطرية
- مبيدات عشبية
- أسمدة
- بذور
- أدوات زراعية
- مستلزمات ري
- أخرى

## وحدات القياس الافتراضية

- كيلو
- لتر
- قطعة
- عبوة
- كيس
- صندوق
- متر
- جرام

## طرق الدفع المدعومة

- نقدي
- شيك
- تحويل بنكي
- بطاقة ائتمان
- آجل

## الدعم الفني

### رسائل الخطأ الشائعة:

**"خطأ في تحميل البيانات"**
- تأكد من وجود ملف قاعدة البيانات
- أعد تشغيل البرنامج

**"فشل في الاتصال بقاعدة البيانات"**
- تأكد من عدم فتح البرنامج في أكثر من مكان
- أعد تشغيل الكمبيوتر

**"Python غير مثبت"**
- حمل وثبت Python من python.org
- تأكد من إضافة Python إلى PATH

### للحصول على مساعدة إضافية:
1. راجع ملف README.md
2. تحقق من رسائل الخطأ في البرنامج
3. تأكد من تحديث البرنامج لآخر إصدار

---

**ملاحظة**: هذا الإصدار الأول من البرنامج. المزيد من المميزات قيد التطوير وستكون متاحة في الإصدارات القادمة.
