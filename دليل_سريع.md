# الدليل السريع - برنامج محاسبة الصيدلية الزراعية

## 🚀 البدء السريع (5 دقائق)

### 1. التثبيت والتشغيل
```bash
# 1. تث<PERSON>يت المتطلبات
install.bat

# 2. إضافة بيانات تجريبية (اختياري)
add_sample_data.bat

# 3. تشغيل البرنامج
run.bat
```

### 2. أول عملية بيع
1. **اضغط "بيع جديد"** من الواجهة الرئيسية
2. **اختر "زبون نقدي"** للبيع النقدي
3. **اختر منتج** من القائمة المنسدلة
4. **أدخل الكمية** (مثال: 2)
5. **اضغط "إضافة"** لإضافة المنتج للبيع
6. **اضغط "حفظ البيع"** وأدخل المبلغ المدفوع
7. **تم!** ✅ تم تسجيل أول عملية بيع

## 📋 العمليات الأساسية

### إدارة المنتجات
| العملية | الخطوات |
|---------|---------|
| **إضافة منتج** | إدارة المنتجات → إضافة منتج جديد → املأ البيانات → حفظ |
| **تحديث المخزون** | إدارة المنتجات → اختر المنتج → تحديث المخزون → أدخل الكمية الجديدة |
| **البحث عن منتج** | إدارة المنتجات → اكتب في مربع البحث |

### إدارة الزبائن
| العملية | الخطوات |
|---------|---------|
| **إضافة زبون** | إدارة الزبائن → إضافة زبون جديد → املأ البيانات → حفظ |
| **تسجيل دفعة** | إدارة الزبائن → اختر الزبون → إضافة دفعة → أدخل المبلغ |
| **عرض المدفوعات** | إدارة الزبائن → اختر الزبون → عرض المدفوعات |

### المبيعات
| العملية | الخطوات |
|---------|---------|
| **بيع نقدي** | بيع جديد → زبون نقدي → أضف المنتجات → حفظ البيع |
| **بيع آجل** | بيع جديد → اختر الزبون → أضف المنتجات → حفظ البيع |
| **عرض المبيعات** | تقارير المبيعات → اختر التاريخ → تصفية |

## 📊 التقارير السريعة

### من الواجهة الرئيسية
- **تقرير الديون**: اضغط "تقرير الديون"
- **تقرير المخزون**: اضغط "تقرير المخزون"

### من قائمة التقارير
- **يومي**: التقارير → تقرير المبيعات اليومية
- **شهري**: التقارير → تقرير المبيعات الشهرية
- **الأرباح**: التقارير → تقرير الأرباح

## 🎨 فهم الألوان

### في جداول المنتجات
- **أبيض**: مخزون طبيعي ✅
- **وردي فاتح**: مخزون منخفض ⚠️
- **وردي غامق**: نفد من المخزون ❌

### في جداول الزبائن
- **أبيض**: لا يوجد ديون ✅
- **أصفر فاتح**: يوجد ديون ⚠️
- **أحمر فاتح**: تجاوز الحد الائتماني ❌

### في جداول المبيعات
- **أبيض**: مدفوع بالكامل ✅
- **أصفر فاتح**: يوجد مبلغ متبقي ⚠️

## 🔧 نصائح سريعة

### لتحسين الأداء
- ✅ أغلق النوافذ غير المستخدمة
- ✅ استخدم البحث بدلاً من التمرير
- ✅ أنشئ نسخة احتياطية أسبوعياً

### لتجنب الأخطاء
- ❌ لا تحذف ملف قاعدة البيانات
- ❌ لا تشغل البرنامج من أكثر من مكان
- ❌ لا تغلق البرنامج بقوة

### للحصول على أفضل النتائج
- 📝 أدخل بيانات دقيقة ومكتملة
- 🔄 حدث المخزون بانتظام
- 📊 راجع التقارير يومياً
- 💾 احتفظ بنسخ احتياطية

## 🆘 حل المشاكل السريع

### مشاكل شائعة
| المشكلة | الحل |
|---------|------|
| **البرنامج لا يفتح** | تأكد من تثبيت Python وشغل install.bat |
| **خطأ في قاعدة البيانات** | أعد تشغيل البرنامج أو استعد نسخة احتياطية |
| **المنتجات لا تظهر** | تأكد من إضافة منتجات أو شغل add_sample_data.bat |
| **لا يمكن حفظ البيع** | تأكد من وجود كمية كافية في المخزون |

### رسائل خطأ شائعة
- **"Python غير مثبت"** → حمل Python من python.org
- **"فشل في تحميل البيانات"** → أعد تشغيل البرنامج
- **"الكمية أكبر من المتوفر"** → تحقق من المخزون أو حدثه

## 📞 الدعم

### للحصول على مساعدة
1. راجع هذا الدليل السريع
2. اقرأ ملف `دليل_المستخدم.md` للتفاصيل
3. تحقق من ملف `README.md` للمعلومات التقنية
4. راجع ملف `CHANGELOG.md` لآخر التحديثات

### معلومات مفيدة
- **ملفات البيانات**: `pharmacy_accounting.db`
- **النسخ الاحتياطية**: مجلد `backups`
- **التقارير**: مجلد `reports`
- **الإعدادات**: `config/settings.py`

---

**نصيحة**: ابدأ بالبيانات التجريبية لفهم البرنامج، ثم احذفها وأدخل بياناتك الحقيقية! 🎯
