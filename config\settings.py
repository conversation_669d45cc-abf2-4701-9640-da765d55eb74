# -*- coding: utf-8 -*-
"""
إعدادات برنامج محاسبة الصيدلية الزراعية
"""

import os
from datetime import datetime

# إعدادات قاعدة البيانات
DATABASE_PATH = "pharmacy_accounting.db"
BACKUP_FOLDER = "backups"

# إعدادات الواجهة
WINDOW_TITLE = "برنامج محاسبة الصيدلية الزراعية"
WINDOW_SIZE = "1200x800"
FONT_FAMILY = "Arial"
FONT_SIZE = 12

# إعدادات العملة
CURRENCY = "ريال"
CURRENCY_SYMBOL = "ر.س"

# إعدادات التقارير
REPORTS_FOLDER = "reports"
COMPANY_NAME = "الصيدلية الزراعية"
COMPANY_ADDRESS = ""
COMPANY_PHONE = ""

# إعد<PERSON><PERSON><PERSON> النسخ الاحتياطية
AUTO_BACKUP = True
BACKUP_INTERVAL_DAYS = 7
MAX_BACKUP_FILES = 30

# فئات المنتجات الافتراضية
DEFAULT_CATEGORIES = [
    "مبيدات حشرية",
    "مبيدات فطرية", 
    "مبيدات عشبية",
    "أسمدة",
    "بذور",
    "أدوات زراعية",
    "مستلزمات ري",
    "أخرى"
]

# وحدات القياس الافتراضية
DEFAULT_UNITS = [
    "كيلو",
    "لتر",
    "قطعة",
    "عبوة",
    "كيس",
    "صندوق",
    "متر",
    "جرام"
]

# طرق الدفع
PAYMENT_METHODS = [
    "نقدي",
    "شيك",
    "تحويل بنكي",
    "بطاقة ائتمان",
    "آجل"
]

# ألوان الواجهة المحسنة
COLORS = {
    'primary': '#1e3a8a',      # أزرق داكن أنيق
    'secondary': '#7c3aed',    # بنفسجي جميل
    'success': '#059669',      # أخضر طبيعي
    'warning': '#dc2626',      # أحمر تحذيري
    'info': '#0891b2',         # أزرق معلوماتي
    'background': '#f8fafc',   # خلفية فاتحة
    'card': '#ffffff',         # خلفية البطاقات
    'text': '#1f2937',         # نص داكن
    'text_light': '#6b7280',   # نص فاتح
    'border': '#e5e7eb',       # حدود فاتحة
    'hover': '#f3f4f6'         # لون التمرير
}

def ensure_folders_exist():
    """التأكد من وجود المجلدات المطلوبة"""
    folders = [BACKUP_FOLDER, REPORTS_FOLDER]
    for folder in folders:
        if not os.path.exists(folder):
            os.makedirs(folder)

def get_backup_filename():
    """إنشاء اسم ملف النسخة الاحتياطية"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"backup_{timestamp}.db"

def get_report_filename(report_type: str):
    """إنشاء اسم ملف التقرير"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"{report_type}_{timestamp}.pdf"
