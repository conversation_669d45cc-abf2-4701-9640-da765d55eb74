# -*- coding: utf-8 -*-
"""
إعدادات برنامج محاسبة الصيدلية الزراعية
"""

import os
from datetime import datetime

# إعدادات قاعدة البيانات
DATABASE_PATH = "pharmacy_accounting.db"
BACKUP_FOLDER = "backups"

# إعدادات الواجهة
WINDOW_TITLE = "برنامج محاسبة الصيدلية الزراعية"
WINDOW_SIZE = "1200x800"
FONT_FAMILY = "Arial"
FONT_SIZE = 12

# إعدادات العملة
CURRENCY = "ريال"
CURRENCY_SYMBOL = "ر.س"

# إعدادات التقارير
REPORTS_FOLDER = "reports"
COMPANY_NAME = "الصيدلية الزراعية"
COMPANY_ADDRESS = ""
COMPANY_PHONE = ""

# إعد<PERSON><PERSON><PERSON> النسخ الاحتياطية
AUTO_BACKUP = True
BACKUP_INTERVAL_DAYS = 7
MAX_BACKUP_FILES = 30

# فئات المنتجات الافتراضية
DEFAULT_CATEGORIES = [
    "مبيدات حشرية",
    "مبيدات فطرية", 
    "مبيدات عشبية",
    "أسمدة",
    "بذور",
    "أدوات زراعية",
    "مستلزمات ري",
    "أخرى"
]

# وحدات القياس الافتراضية
DEFAULT_UNITS = [
    "كيلو",
    "لتر",
    "قطعة",
    "عبوة",
    "كيس",
    "صندوق",
    "متر",
    "جرام"
]

# طرق الدفع
PAYMENT_METHODS = [
    "نقدي",
    "شيك",
    "تحويل بنكي",
    "بطاقة ائتمان",
    "آجل"
]

# ألوان الواجهة
COLORS = {
    'primary': '#2E86AB',
    'secondary': '#A23B72', 
    'success': '#F18F01',
    'warning': '#C73E1D',
    'background': '#F5F5F5',
    'text': '#333333'
}

def ensure_folders_exist():
    """التأكد من وجود المجلدات المطلوبة"""
    folders = [BACKUP_FOLDER, REPORTS_FOLDER]
    for folder in folders:
        if not os.path.exists(folder):
            os.makedirs(folder)

def get_backup_filename():
    """إنشاء اسم ملف النسخة الاحتياطية"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"backup_{timestamp}.db"

def get_report_filename(report_type: str):
    """إنشاء اسم ملف التقرير"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"{report_type}_{timestamp}.pdf"
