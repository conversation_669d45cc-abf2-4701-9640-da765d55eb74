# -*- coding: utf-8 -*-
"""
اختبار إضافة الموردين والمنتجات
"""

import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.database import PharmacyDatabase

def test_suppliers_and_products():
    """اختبار إضافة الموردين والمنتجات"""
    print("🧪 اختبار إضافة الموردين والمنتجات...")
    
    try:
        # إنشاء قاعدة بيانات اختبار
        db = PharmacyDatabase("test_suppliers.db")
        print("✅ تم إنشاء قاعدة البيانات بنجاح")
        
        # اختبار إضافة مورد
        print("\n📋 اختبار إضافة مورد...")
        supplier_id = db.add_supplier(
            name="مورد اختبار",
            phone="0501234567",
            address="الرياض، المملكة العربية السعودية",
            email="<EMAIL>"
        )
        print(f"✅ تم إضافة مورد بالمعرف: {supplier_id}")
        
        # اختبار الحصول على الموردين
        suppliers = db.get_all_suppliers()
        print(f"✅ تم جلب {len(suppliers)} مورد من قاعدة البيانات")
        
        # اختبار إضافة منتج
        print("\n📦 اختبار إضافة منتج...")
        product_id = db.add_product(
            name="منتج اختبار",
            category="أدوية",
            unit="حبة",
            purchase_price=10.0,
            selling_price=15.0,
            stock_quantity=100,
            min_stock_level=10,
            supplier_id=supplier_id
        )
        print(f"✅ تم إضافة منتج بالمعرف: {product_id}")
        
        # اختبار الحصول على المنتجات
        products = db.get_all_products()
        print(f"✅ تم جلب {len(products)} منتج من قاعدة البيانات")
        
        # اختبار تحديث المنتج
        print("\n🔄 اختبار تحديث المنتج...")
        db.update_product(
            product_id=product_id,
            name="منتج اختبار محدث",
            selling_price=20.0,
            stock_quantity=150
        )
        print("✅ تم تحديث المنتج بنجاح")
        
        # اختبار الحصول على منتج بالمعرف
        product = db.get_product_by_id(product_id)
        if product:
            print(f"✅ تم جلب المنتج: {product['name']}")
            print(f"   السعر الجديد: {product['selling_price']}")
            print(f"   الكمية الجديدة: {product['stock_quantity']}")
        
        # اختبار إضافة منتج آخر
        print("\n📦 اختبار إضافة منتج آخر...")
        product2_id = db.add_product(
            name="مبيد حشري",
            category="مبيدات",
            unit="لتر",
            purchase_price=45.0,
            selling_price=60.0,
            stock_quantity=25,
            min_stock_level=5,
            supplier_id=supplier_id
        )
        print(f"✅ تم إضافة المنتج الثاني بالمعرف: {product2_id}")
        
        # اختبار الحصول على منتجات المورد
        print("\n🔍 اختبار جلب منتجات المورد...")
        conn = db.db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id, name, category, unit, purchase_price, selling_price, stock_quantity, min_stock_level
            FROM products
            WHERE supplier_id = ?
            ORDER BY name
        ''', (supplier_id,))
        supplier_products = cursor.fetchall()
        conn.close()
        
        print(f"✅ المورد لديه {len(supplier_products)} منتج:")
        for product in supplier_products:
            print(f"   - {product[1]} ({product[2]}) - {product[6]} {product[3]}")
        
        # تنظيف ملف الاختبار
        os.remove("test_suppliers.db")
        print("\n🗑️ تم حذف قاعدة بيانات الاختبار")
        
        print("\n🎉 جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"\n❌ فشل الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # تنظيف في حالة الخطأ
        try:
            os.remove("test_suppliers.db")
        except:
            pass
        
        return False

if __name__ == "__main__":
    test_suppliers_and_products()
