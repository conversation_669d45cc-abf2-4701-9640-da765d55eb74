# -*- coding: utf-8 -*-
"""
واجهة إدارة الزبائن لبرنامج محاسبة الصيدلية الزراعية
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import *
from database.database import PharmacyDatabase

class CustomersWindow:
    """نافذة إدارة الزبائن"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.db = PharmacyDatabase()
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.setup_window()
        self.create_interface()
        self.load_customers()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة الزبائن")
        self.window.geometry("1100x700")
        self.window.configure(bg=COLORS['background'])
        self.center_window()
        
    def center_window(self):
        """وضع النافذة في منتصف الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg=COLORS['secondary'], height=60)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="إدارة الزبائن",
                              font=(FONT_FAMILY, 18, 'bold'),
                              bg=COLORS['secondary'], fg='white')
        title_label.pack(expand=True)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # أزرار العمليات
        tk.Button(buttons_frame, text="إضافة زبون جديد",
                 command=self.add_customer,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['success'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تعديل زبون",
                 command=self.edit_customer,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['primary'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="حذف زبون",
                 command=self.delete_customer,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['warning'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="إضافة دفعة",
                 command=self.add_payment,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['secondary'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="عرض المدفوعات",
                 command=self.view_payments,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg='#6c757d', fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        # إطار البحث
        search_frame = tk.Frame(self.window, bg=COLORS['background'])
        search_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(search_frame, text="البحث:",
                font=(FONT_FAMILY, 12),
                bg=COLORS['background']).pack(side=tk.LEFT, padx=5)
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.filter_customers)
        search_entry = tk.Entry(search_frame, textvariable=self.search_var,
                               font=(FONT_FAMILY, 12), width=30)
        search_entry.pack(side=tk.LEFT, padx=5)
        
        # إطار الجدول
        table_frame = tk.Frame(self.window, bg=COLORS['background'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # إنشاء الجدول
        self.create_customers_table(table_frame)
        
        # إطار المعلومات
        info_frame = tk.Frame(self.window, bg=COLORS['background'])
        info_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.info_label = tk.Label(info_frame, text="",
                                  font=(FONT_FAMILY, 10),
                                  bg=COLORS['background'])
        self.info_label.pack(anchor=tk.W)
        
    def create_customers_table(self, parent):
        """إنشاء جدول الزبائن"""
        columns = ('id', 'name', 'phone', 'address', 'email', 'credit_limit', 'current_debt')
        
        self.tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة
        headers = {
            'id': 'الرقم',
            'name': 'الاسم',
            'phone': 'الهاتف',
            'address': 'العنوان',
            'email': 'البريد الإلكتروني',
            'credit_limit': 'الحد الائتماني',
            'current_debt': 'الدين الحالي'
        }
        
        for col in columns:
            self.tree.heading(col, text=headers[col])
            if col == 'id':
                self.tree.column(col, width=60, anchor=tk.CENTER)
            elif col in ['credit_limit', 'current_debt']:
                self.tree.column(col, width=100, anchor=tk.CENTER)
            elif col in ['phone']:
                self.tree.column(col, width=120, anchor=tk.CENTER)
            else:
                self.tree.column(col, width=150, anchor=tk.CENTER)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.tree.bind('<Double-1>', self.on_item_double_click)
        
    def load_customers(self):
        """تحميل الزبائن في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل الزبائن من قاعدة البيانات
            customers = self.db.get_all_customers()
            
            total_debt = 0
            customers_with_debt = 0
            
            for customer in customers:
                # تلوين الصفوف حسب الديون
                tags = []
                if customer['current_debt'] > 0:
                    tags.append('has_debt')
                    total_debt += customer['current_debt']
                    customers_with_debt += 1
                elif customer['current_debt'] > customer['credit_limit']:
                    tags.append('over_limit')
                
                self.tree.insert('', tk.END, values=(
                    customer['id'],
                    customer['name'],
                    customer['phone'] or '',
                    customer['address'] or '',
                    customer['email'] or '',
                    f"{customer['credit_limit']:.2f}",
                    f"{customer['current_debt']:.2f}"
                ), tags=tags)
            
            # تعيين ألوان للصفوف
            self.tree.tag_configure('has_debt', background='#fff3cd')
            self.tree.tag_configure('over_limit', background='#f8d7da')
            
            # تحديث معلومات الإحصائيات
            self.update_info_label(len(customers), customers_with_debt, total_debt)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الزبائن: {str(e)}")
    
    def update_info_label(self, total_customers, customers_with_debt, total_debt):
        """تحديث تسمية المعلومات"""
        info_text = f"إجمالي الزبائن: {total_customers} | زبائن لديهم ديون: {customers_with_debt} | إجمالي الديون: {total_debt:.2f} {CURRENCY_SYMBOL}"
        self.info_label.config(text=info_text)
    
    def filter_customers(self, *args):
        """تصفية الزبائن حسب النص المدخل"""
        search_text = self.search_var.get().lower()
        
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        try:
            # تحميل جميع الزبائن
            customers = self.db.get_all_customers()
            
            # تصفية الزبائن
            filtered_customers = []
            for customer in customers:
                if (search_text in customer['name'].lower() or 
                    search_text in (customer['phone'] or '').lower() or
                    search_text in (customer['address'] or '').lower()):
                    filtered_customers.append(customer)
            
            # عرض الزبائن المصفاة
            total_debt = 0
            customers_with_debt = 0
            
            for customer in filtered_customers:
                tags = []
                if customer['current_debt'] > 0:
                    tags.append('has_debt')
                    total_debt += customer['current_debt']
                    customers_with_debt += 1
                elif customer['current_debt'] > customer['credit_limit']:
                    tags.append('over_limit')
                
                self.tree.insert('', tk.END, values=(
                    customer['id'],
                    customer['name'],
                    customer['phone'] or '',
                    customer['address'] or '',
                    customer['email'] or '',
                    f"{customer['credit_limit']:.2f}",
                    f"{customer['current_debt']:.2f}"
                ), tags=tags)
            
            # تحديث المعلومات
            self.update_info_label(len(filtered_customers), customers_with_debt, total_debt)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصفية الزبائن: {str(e)}")
    
    def add_customer(self):
        """إضافة زبون جديد"""
        dialog = CustomerDialog(self.window, "إضافة زبون جديد")
        if dialog.result:
            try:
                customer_data = dialog.result
                self.db.add_customer(
                    name=customer_data['name'],
                    phone=customer_data['phone'],
                    address=customer_data['address'],
                    email=customer_data['email'],
                    credit_limit=customer_data['credit_limit']
                )
                messagebox.showinfo("نجح", "تم إضافة الزبون بنجاح")
                self.load_customers()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة الزبون: {str(e)}")
    
    def edit_customer(self):
        """تعديل زبون محدد"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار زبون للتعديل")
            return
        
        # الحصول على بيانات الزبون المحدد
        item_values = self.tree.item(selected_item[0])['values']
        customer_id = item_values[0]
        
        # الحصول على البيانات الكاملة من قاعدة البيانات
        customer = self.db.get_customer_by_id(customer_id)
        if not customer:
            messagebox.showerror("خطأ", "لم يتم العثور على الزبون")
            return
        
        # فتح نافذة التعديل
        dialog = CustomerDialog(self.window, "تعديل زبون", customer)
        if dialog.result:
            try:
                customer_data = dialog.result
                self.db.update_customer(
                    customer_id=customer_id,
                    name=customer_data['name'],
                    phone=customer_data['phone'],
                    address=customer_data['address'],
                    email=customer_data['email'],
                    credit_limit=customer_data['credit_limit']
                )
                messagebox.showinfo("نجح", "تم تعديل الزبون بنجاح")
                self.load_customers()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تعديل الزبون: {str(e)}")
    
    def delete_customer(self):
        """حذف زبون محدد"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار زبون للحذف")
            return
        
        # الحصول على بيانات الزبون
        item_values = self.tree.item(selected_item[0])['values']
        customer_id = item_values[0]
        customer_name = item_values[1]
        
        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف", 
                              f"هل أنت متأكد من حذف الزبون '{customer_name}'؟\n\nتحذير: لا يمكن التراجع عن هذا الإجراء"):
            try:
                self.db.delete_customer(customer_id)
                messagebox.showinfo("نجح", "تم حذف الزبون بنجاح")
                self.load_customers()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف الزبون: {str(e)}")
    
    def add_payment(self):
        """إضافة دفعة لزبون"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار زبون لإضافة دفعة")
            return
        
        # الحصول على بيانات الزبون
        item_values = self.tree.item(selected_item[0])['values']
        customer_id = item_values[0]
        customer_name = item_values[1]
        current_debt = float(item_values[6])
        
        if current_debt <= 0:
            messagebox.showinfo("معلومة", "هذا الزبون ليس لديه ديون")
            return
        
        # طلب مبلغ الدفعة
        amount = simpledialog.askfloat(
            "إضافة دفعة",
            f"الزبون: {customer_name}\nالدين الحالي: {current_debt:.2f} {CURRENCY_SYMBOL}\n\nأدخل مبلغ الدفعة:",
            minvalue=0.01,
            maxvalue=current_debt
        )
        
        if amount:
            try:
                self.db.add_payment(
                    customer_id=customer_id,
                    amount=amount,
                    payment_type='استلام',
                    payment_method='نقدي',
                    notes=f"دفعة من الزبون {customer_name}"
                )
                messagebox.showinfo("نجح", f"تم تسجيل الدفعة بمبلغ {amount:.2f} {CURRENCY_SYMBOL}")
                self.load_customers()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تسجيل الدفعة: {str(e)}")
    
    def view_payments(self):
        """عرض مدفوعات زبون"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار زبون لعرض مدفوعاته")
            return
        
        # الحصول على بيانات الزبون
        item_values = self.tree.item(selected_item[0])['values']
        customer_id = item_values[0]
        customer_name = item_values[1]
        
        try:
            payments = self.db.get_customer_payments(customer_id)
            
            if not payments:
                messagebox.showinfo("معلومة", f"لا توجد مدفوعات للزبون '{customer_name}'")
                return
            
            # إنشاء نافذة عرض المدفوعات
            PaymentsWindow(self.window, customer_name, payments)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المدفوعات: {str(e)}")
    
    def on_item_double_click(self, event):
        """التعامل مع النقر المزدوج على عنصر"""
        self.edit_customer()

class CustomerDialog:
    """نافذة حوار إضافة/تعديل زبون"""

    def __init__(self, parent, title="إضافة زبون", customer_data=None):
        self.result = None
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("450x400")
        self.dialog.configure(bg=COLORS['background'])
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.customer_data = customer_data
        self.create_form()
        self.center_dialog()

    def center_dialog(self):
        """وضع النافذة في المنتصف"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f'{width}x{height}+{x}+{y}')

    def create_form(self):
        """إنشاء نموذج إدخال البيانات"""
        # إطار النموذج
        form_frame = tk.Frame(self.dialog, bg=COLORS['background'])
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # حقول الإدخال
        self.entries = {}

        fields = [
            ('name', 'اسم الزبون', 'str', True),
            ('phone', 'رقم الهاتف', 'str', False),
            ('address', 'العنوان', 'str', False),
            ('email', 'البريد الإلكتروني', 'str', False),
            ('credit_limit', 'الحد الائتماني', 'float', False)
        ]

        for i, (field, label, field_type, required) in enumerate(fields):
            # تسمية الحقل
            label_text = label + ("*" if required else "") + ":"
            tk.Label(form_frame, text=label_text,
                    font=(FONT_FAMILY, 12),
                    bg=COLORS['background']).grid(row=i, column=0, sticky=tk.W, pady=8)

            # حقل الإدخال
            entry = tk.Entry(form_frame, font=(FONT_FAMILY, 12), width=30)
            entry.grid(row=i, column=1, sticky=tk.W, pady=8, padx=10)
            self.entries[field] = entry

            # تعبئة البيانات الموجودة (في حالة التعديل)
            if self.customer_data and field in self.customer_data:
                value = self.customer_data[field]
                if value is not None:
                    entry.insert(0, str(value))

        # ملاحظة الحقول المطلوبة
        note_label = tk.Label(form_frame, text="* حقول مطلوبة",
                             font=(FONT_FAMILY, 10),
                             bg=COLORS['background'], fg='red')
        note_label.grid(row=len(fields), column=0, columnspan=2, sticky=tk.W, pady=5)

        # أزرار العمليات
        buttons_frame = tk.Frame(form_frame, bg=COLORS['background'])
        buttons_frame.grid(row=len(fields)+1, column=0, columnspan=2, pady=20)

        tk.Button(buttons_frame, text="حفظ",
                 command=self.save_customer,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['success'], fg='white',
                 width=12).pack(side=tk.LEFT, padx=10)

        tk.Button(buttons_frame, text="إلغاء",
                 command=self.dialog.destroy,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['warning'], fg='white',
                 width=12).pack(side=tk.LEFT, padx=10)

    def save_customer(self):
        """حفظ بيانات الزبون"""
        try:
            # التحقق من صحة البيانات
            data = {}

            # اسم الزبون (مطلوب)
            name = self.entries['name'].get().strip()
            if not name:
                raise ValueError("يرجى إدخال اسم الزبون")
            data['name'] = name

            # رقم الهاتف
            phone = self.entries['phone'].get().strip()
            data['phone'] = phone if phone else None

            # العنوان
            address = self.entries['address'].get().strip()
            data['address'] = address if address else None

            # البريد الإلكتروني
            email = self.entries['email'].get().strip()
            if email and '@' not in email:
                raise ValueError("يرجى إدخال بريد إلكتروني صحيح")
            data['email'] = email if email else None

            # الحد الائتماني
            try:
                credit_limit = float(self.entries['credit_limit'].get() or 0)
                if credit_limit < 0:
                    raise ValueError("الحد الائتماني يجب أن يكون أكبر من أو يساوي صفر")
                data['credit_limit'] = credit_limit
            except ValueError:
                raise ValueError("يرجى إدخال حد ائتماني صحيح")

            # حفظ البيانات
            self.result = data
            self.dialog.destroy()

        except ValueError as e:
            messagebox.showerror("خطأ في البيانات", str(e))

class PaymentsWindow:
    """نافذة عرض مدفوعات الزبون"""

    def __init__(self, parent, customer_name, payments):
        self.window = tk.Toplevel(parent)
        self.window.title(f"مدفوعات الزبون: {customer_name}")
        self.window.geometry("800x500")
        self.window.configure(bg=COLORS['background'])
        self.window.transient(parent)

        self.payments = payments
        self.create_interface()
        self.load_payments()
        self.center_window()

    def center_window(self):
        """وضع النافذة في المنتصف"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def create_interface(self):
        """إنشاء واجهة عرض المدفوعات"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg=COLORS['primary'], height=50)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="سجل المدفوعات",
                              font=(FONT_FAMILY, 16, 'bold'),
                              bg=COLORS['primary'], fg='white')
        title_label.pack(expand=True)

        # إطار الجدول
        table_frame = tk.Frame(self.window, bg=COLORS['background'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # إنشاء جدول المدفوعات
        columns = ('id', 'amount', 'payment_type', 'payment_method', 'payment_date', 'notes')

        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # تعيين عناوين الأعمدة
        headers = {
            'id': 'الرقم',
            'amount': 'المبلغ',
            'payment_type': 'النوع',
            'payment_method': 'طريقة الدفع',
            'payment_date': 'التاريخ',
            'notes': 'ملاحظات'
        }

        for col in columns:
            self.tree.heading(col, text=headers[col])
            if col == 'id':
                self.tree.column(col, width=60, anchor=tk.CENTER)
            elif col == 'amount':
                self.tree.column(col, width=100, anchor=tk.CENTER)
            elif col in ['payment_type', 'payment_method']:
                self.tree.column(col, width=100, anchor=tk.CENTER)
            elif col == 'payment_date':
                self.tree.column(col, width=120, anchor=tk.CENTER)
            else:
                self.tree.column(col, width=200, anchor=tk.CENTER)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # إطار الملخص
        summary_frame = tk.Frame(self.window, bg=COLORS['background'])
        summary_frame.pack(fill=tk.X, padx=10, pady=5)

        self.summary_label = tk.Label(summary_frame, text="",
                                     font=(FONT_FAMILY, 12, 'bold'),
                                     bg=COLORS['background'])
        self.summary_label.pack()

        # زر الإغلاق
        close_button = tk.Button(self.window, text="إغلاق",
                               command=self.window.destroy,
                               font=(FONT_FAMILY, 12, 'bold'),
                               bg=COLORS['warning'], fg='white',
                               width=15)
        close_button.pack(pady=10)

    def load_payments(self):
        """تحميل المدفوعات في الجدول"""
        total_amount = 0

        for payment in self.payments:
            self.tree.insert('', tk.END, values=(
                payment['id'],
                f"{payment['amount']:.2f}",
                payment['payment_type'],
                payment['payment_method'],
                payment['payment_date'],
                payment['notes'] or ''
            ))

            if payment['payment_type'] == 'استلام':
                total_amount += payment['amount']
            else:
                total_amount -= payment['amount']

        # تحديث الملخص
        self.summary_label.config(
            text=f"إجمالي المدفوعات: {len(self.payments)} | المبلغ الإجمالي: {total_amount:.2f} {CURRENCY_SYMBOL}"
        )

if __name__ == "__main__":
    # اختبار النافذة
    app = CustomersWindow()
    app.window.mainloop()
