# -*- coding: utf-8 -*-
"""
واجهة إدارة الزبائن لبرنامج محاسبة الصيدلية الزراعية
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import *
from database.database import PharmacyDatabase

class CustomersWindow:
    """نافذة إدارة الزبائن"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.db = PharmacyDatabase()
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.setup_window()
        self.create_interface()
        self.load_customers()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة الزبائن")
        self.window.geometry("1100x700")
        self.window.configure(bg=COLORS['background'])
        self.center_window()
        
    def center_window(self):
        """وضع النافذة في منتصف الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg=COLORS['secondary'], height=60)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="إدارة الزبائن",
                              font=(FONT_FAMILY, 18, 'bold'),
                              bg=COLORS['secondary'], fg='white')
        title_label.pack(expand=True)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # أزرار العمليات
        tk.Button(buttons_frame, text="إضافة زبون جديد",
                 command=self.add_customer,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['success'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تعديل زبون",
                 command=self.edit_customer,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['primary'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="حذف زبون",
                 command=self.delete_customer,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['warning'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="إضافة دفعة",
                 command=self.add_payment,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['secondary'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تفاصيل الزبون",
                 command=self.view_customer_details,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg='#6c757d', fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        # إطار البحث
        search_frame = tk.Frame(self.window, bg=COLORS['background'])
        search_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(search_frame, text="البحث:",
                font=(FONT_FAMILY, 12),
                bg=COLORS['background']).pack(side=tk.LEFT, padx=5)
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.filter_customers)
        search_entry = tk.Entry(search_frame, textvariable=self.search_var,
                               font=(FONT_FAMILY, 12), width=30)
        search_entry.pack(side=tk.LEFT, padx=5)
        
        # إطار الجدول
        table_frame = tk.Frame(self.window, bg=COLORS['background'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # إنشاء الجدول
        self.create_customers_table(table_frame)
        
        # إطار المعلومات
        info_frame = tk.Frame(self.window, bg=COLORS['background'])
        info_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.info_label = tk.Label(info_frame, text="",
                                  font=(FONT_FAMILY, 10),
                                  bg=COLORS['background'])
        self.info_label.pack(anchor=tk.W)
        
    def create_customers_table(self, parent):
        """إنشاء جدول الزبائن"""
        columns = ('id', 'name', 'phone', 'address', 'email', 'credit_limit', 'current_debt')
        
        self.tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة
        headers = {
            'id': 'الرقم',
            'name': 'الاسم',
            'phone': 'الهاتف',
            'address': 'العنوان',
            'email': 'البريد الإلكتروني',
            'credit_limit': 'الحد الائتماني',
            'current_debt': 'الدين الحالي'
        }
        
        for col in columns:
            self.tree.heading(col, text=headers[col])
            if col == 'id':
                self.tree.column(col, width=60, anchor=tk.CENTER)
            elif col in ['credit_limit', 'current_debt']:
                self.tree.column(col, width=100, anchor=tk.CENTER)
            elif col in ['phone']:
                self.tree.column(col, width=120, anchor=tk.CENTER)
            else:
                self.tree.column(col, width=150, anchor=tk.CENTER)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.tree.bind('<Double-1>', self.on_item_double_click)
        
    def load_customers(self):
        """تحميل الزبائن في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل الزبائن من قاعدة البيانات
            customers = self.db.get_all_customers()
            
            total_debt = 0
            customers_with_debt = 0
            
            for customer in customers:
                # تلوين الصفوف حسب الديون
                tags = []
                if customer['current_debt'] > 0:
                    tags.append('has_debt')
                    total_debt += customer['current_debt']
                    customers_with_debt += 1
                elif customer['current_debt'] > customer['credit_limit']:
                    tags.append('over_limit')
                
                self.tree.insert('', tk.END, values=(
                    customer['id'],
                    customer['name'],
                    customer['phone'] or '',
                    customer['address'] or '',
                    customer['email'] or '',
                    f"{customer['credit_limit']:.2f}",
                    f"{customer['current_debt']:.2f}"
                ), tags=tags)
            
            # تعيين ألوان للصفوف
            self.tree.tag_configure('has_debt', background='#fff3cd')
            self.tree.tag_configure('over_limit', background='#f8d7da')
            
            # تحديث معلومات الإحصائيات
            self.update_info_label(len(customers), customers_with_debt, total_debt)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الزبائن: {str(e)}")
    
    def update_info_label(self, total_customers, customers_with_debt, total_debt):
        """تحديث تسمية المعلومات"""
        info_text = f"إجمالي الزبائن: {total_customers} | زبائن لديهم ديون: {customers_with_debt} | إجمالي الديون: {total_debt:.2f} {CURRENCY_SYMBOL}"
        self.info_label.config(text=info_text)
    
    def filter_customers(self, *args):
        """تصفية الزبائن حسب النص المدخل"""
        search_text = self.search_var.get().lower()
        
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        try:
            # تحميل جميع الزبائن
            customers = self.db.get_all_customers()
            
            # تصفية الزبائن
            filtered_customers = []
            for customer in customers:
                if (search_text in customer['name'].lower() or 
                    search_text in (customer['phone'] or '').lower() or
                    search_text in (customer['address'] or '').lower()):
                    filtered_customers.append(customer)
            
            # عرض الزبائن المصفاة
            total_debt = 0
            customers_with_debt = 0
            
            for customer in filtered_customers:
                tags = []
                if customer['current_debt'] > 0:
                    tags.append('has_debt')
                    total_debt += customer['current_debt']
                    customers_with_debt += 1
                elif customer['current_debt'] > customer['credit_limit']:
                    tags.append('over_limit')
                
                self.tree.insert('', tk.END, values=(
                    customer['id'],
                    customer['name'],
                    customer['phone'] or '',
                    customer['address'] or '',
                    customer['email'] or '',
                    f"{customer['credit_limit']:.2f}",
                    f"{customer['current_debt']:.2f}"
                ), tags=tags)
            
            # تحديث المعلومات
            self.update_info_label(len(filtered_customers), customers_with_debt, total_debt)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصفية الزبائن: {str(e)}")
    
    def add_customer(self):
        """إضافة زبون جديد"""
        dialog = CustomerDialog(self.window, "إضافة زبون جديد")
        if dialog.result:
            try:
                customer_data = dialog.result
                self.db.add_customer(
                    name=customer_data['name'],
                    phone=customer_data['phone'],
                    address=customer_data['address'],
                    email=customer_data['email'],
                    credit_limit=customer_data['credit_limit']
                )
                messagebox.showinfo("نجح", "تم إضافة الزبون بنجاح")
                self.load_customers()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة الزبون: {str(e)}")
    
    def edit_customer(self):
        """تعديل زبون محدد"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار زبون للتعديل")
            return
        
        # الحصول على بيانات الزبون المحدد
        item_values = self.tree.item(selected_item[0])['values']
        customer_id = item_values[0]
        
        # الحصول على البيانات الكاملة من قاعدة البيانات
        customer = self.db.get_customer_by_id(customer_id)
        if not customer:
            messagebox.showerror("خطأ", "لم يتم العثور على الزبون")
            return
        
        # فتح نافذة التعديل
        dialog = CustomerDialog(self.window, "تعديل زبون", customer)
        if dialog.result:
            try:
                customer_data = dialog.result
                self.db.update_customer(
                    customer_id=customer_id,
                    name=customer_data['name'],
                    phone=customer_data['phone'],
                    address=customer_data['address'],
                    email=customer_data['email'],
                    credit_limit=customer_data['credit_limit']
                )
                messagebox.showinfo("نجح", "تم تعديل الزبون بنجاح")
                self.load_customers()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تعديل الزبون: {str(e)}")
    
    def delete_customer(self):
        """حذف زبون محدد"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار زبون للحذف")
            return
        
        # الحصول على بيانات الزبون
        item_values = self.tree.item(selected_item[0])['values']
        customer_id = item_values[0]
        customer_name = item_values[1]
        
        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف", 
                              f"هل أنت متأكد من حذف الزبون '{customer_name}'؟\n\nتحذير: لا يمكن التراجع عن هذا الإجراء"):
            try:
                self.db.delete_customer(customer_id)
                messagebox.showinfo("نجح", "تم حذف الزبون بنجاح")
                self.load_customers()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف الزبون: {str(e)}")
    
    def add_payment(self):
        """إضافة دفعة لزبون"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار زبون لإضافة دفعة")
            return
        
        # الحصول على بيانات الزبون
        item_values = self.tree.item(selected_item[0])['values']
        customer_id = item_values[0]
        customer_name = item_values[1]
        current_debt = float(item_values[6])
        
        if current_debt <= 0:
            messagebox.showinfo("معلومة", "هذا الزبون ليس لديه ديون")
            return
        
        # طلب مبلغ الدفعة
        amount = simpledialog.askfloat(
            "إضافة دفعة",
            f"الزبون: {customer_name}\nالدين الحالي: {current_debt:.2f} {CURRENCY_SYMBOL}\n\nأدخل مبلغ الدفعة:",
            minvalue=0.01,
            maxvalue=current_debt
        )
        
        if amount:
            try:
                self.db.add_payment(
                    customer_id=customer_id,
                    amount=amount,
                    payment_type='استلام',
                    payment_method='نقدي',
                    notes=f"دفعة من الزبون {customer_name}"
                )
                messagebox.showinfo("نجح", f"تم تسجيل الدفعة بمبلغ {amount:.2f} {CURRENCY_SYMBOL}")
                self.load_customers()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تسجيل الدفعة: {str(e)}")
    
    def view_payments(self):
        """عرض مدفوعات زبون"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار زبون لعرض مدفوعاته")
            return

        # الحصول على بيانات الزبون
        item_values = self.tree.item(selected_item[0])['values']
        customer_id = item_values[0]
        customer_name = item_values[1]

        try:
            # فتح نافذة تفاصيل الزبون الشاملة
            CustomerDetailsWindow(self.window, customer_id, customer_name, self.db)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل تفاصيل الزبون: {str(e)}")

    def view_customer_details(self):
        """عرض تفاصيل الزبون الشاملة"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار زبون لعرض تفاصيله")
            return

        # الحصول على بيانات الزبون
        item_values = self.tree.item(selected_item[0])['values']
        customer_id = item_values[0]
        customer_name = item_values[1]

        try:
            # فتح نافذة تفاصيل الزبون الشاملة
            CustomerDetailsWindow(self.window, customer_id, customer_name, self.db)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل تفاصيل الزبون: {str(e)}")
    
    def on_item_double_click(self, event):
        """التعامل مع النقر المزدوج على عنصر"""
        self.edit_customer()

class CustomerDialog:
    """نافذة حوار إضافة/تعديل زبون"""

    def __init__(self, parent, title="إضافة زبون", customer_data=None):
        self.result = None
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("450x400")
        self.dialog.configure(bg=COLORS['background'])
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.customer_data = customer_data
        self.create_form()
        self.center_dialog()

    def center_dialog(self):
        """وضع النافذة في المنتصف"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f'{width}x{height}+{x}+{y}')

    def create_form(self):
        """إنشاء نموذج إدخال البيانات"""
        # إطار النموذج
        form_frame = tk.Frame(self.dialog, bg=COLORS['background'])
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # حقول الإدخال
        self.entries = {}

        fields = [
            ('name', 'اسم الزبون', 'str', True),
            ('phone', 'رقم الهاتف', 'str', False),
            ('address', 'العنوان', 'str', False),
            ('email', 'البريد الإلكتروني', 'str', False),
            ('credit_limit', 'الحد الائتماني', 'float', False)
        ]

        for i, (field, label, field_type, required) in enumerate(fields):
            # تسمية الحقل
            label_text = label + ("*" if required else "") + ":"
            tk.Label(form_frame, text=label_text,
                    font=(FONT_FAMILY, 12),
                    bg=COLORS['background']).grid(row=i, column=0, sticky=tk.W, pady=8)

            # حقل الإدخال
            entry = tk.Entry(form_frame, font=(FONT_FAMILY, 12), width=30)
            entry.grid(row=i, column=1, sticky=tk.W, pady=8, padx=10)
            self.entries[field] = entry

            # تعبئة البيانات الموجودة (في حالة التعديل)
            if self.customer_data and field in self.customer_data:
                value = self.customer_data[field]
                if value is not None:
                    entry.insert(0, str(value))

        # ملاحظة الحقول المطلوبة
        note_label = tk.Label(form_frame, text="* حقول مطلوبة",
                             font=(FONT_FAMILY, 10),
                             bg=COLORS['background'], fg='red')
        note_label.grid(row=len(fields), column=0, columnspan=2, sticky=tk.W, pady=5)

        # أزرار العمليات
        buttons_frame = tk.Frame(form_frame, bg=COLORS['background'])
        buttons_frame.grid(row=len(fields)+1, column=0, columnspan=2, pady=20)

        tk.Button(buttons_frame, text="حفظ",
                 command=self.save_customer,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['success'], fg='white',
                 width=12).pack(side=tk.LEFT, padx=10)

        tk.Button(buttons_frame, text="إلغاء",
                 command=self.dialog.destroy,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['warning'], fg='white',
                 width=12).pack(side=tk.LEFT, padx=10)

    def save_customer(self):
        """حفظ بيانات الزبون"""
        try:
            # التحقق من صحة البيانات
            data = {}

            # اسم الزبون (مطلوب)
            name = self.entries['name'].get().strip()
            if not name:
                raise ValueError("يرجى إدخال اسم الزبون")
            data['name'] = name

            # رقم الهاتف
            phone = self.entries['phone'].get().strip()
            data['phone'] = phone if phone else None

            # العنوان
            address = self.entries['address'].get().strip()
            data['address'] = address if address else None

            # البريد الإلكتروني
            email = self.entries['email'].get().strip()
            if email and '@' not in email:
                raise ValueError("يرجى إدخال بريد إلكتروني صحيح")
            data['email'] = email if email else None

            # الحد الائتماني
            try:
                credit_limit = float(self.entries['credit_limit'].get() or 0)
                if credit_limit < 0:
                    raise ValueError("الحد الائتماني يجب أن يكون أكبر من أو يساوي صفر")
                data['credit_limit'] = credit_limit
            except ValueError:
                raise ValueError("يرجى إدخال حد ائتماني صحيح")

            # حفظ البيانات
            self.result = data
            self.dialog.destroy()

        except ValueError as e:
            messagebox.showerror("خطأ في البيانات", str(e))

class PaymentsWindow:
    """نافذة عرض مدفوعات الزبون"""

    def __init__(self, parent, customer_name, payments):
        self.window = tk.Toplevel(parent)
        self.window.title(f"مدفوعات الزبون: {customer_name}")
        self.window.geometry("800x500")
        self.window.configure(bg=COLORS['background'])
        self.window.transient(parent)

        self.payments = payments
        self.create_interface()
        self.load_payments()
        self.center_window()

    def center_window(self):
        """وضع النافذة في المنتصف"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def create_interface(self):
        """إنشاء واجهة عرض المدفوعات"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg=COLORS['primary'], height=50)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="سجل المدفوعات",
                              font=(FONT_FAMILY, 16, 'bold'),
                              bg=COLORS['primary'], fg='white')
        title_label.pack(expand=True)

        # إطار الجدول
        table_frame = tk.Frame(self.window, bg=COLORS['background'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # إنشاء جدول المدفوعات
        columns = ('id', 'amount', 'payment_type', 'payment_method', 'payment_date', 'notes')

        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # تعيين عناوين الأعمدة
        headers = {
            'id': 'الرقم',
            'amount': 'المبلغ',
            'payment_type': 'النوع',
            'payment_method': 'طريقة الدفع',
            'payment_date': 'التاريخ',
            'notes': 'ملاحظات'
        }

        for col in columns:
            self.tree.heading(col, text=headers[col])
            if col == 'id':
                self.tree.column(col, width=60, anchor=tk.CENTER)
            elif col == 'amount':
                self.tree.column(col, width=100, anchor=tk.CENTER)
            elif col in ['payment_type', 'payment_method']:
                self.tree.column(col, width=100, anchor=tk.CENTER)
            elif col == 'payment_date':
                self.tree.column(col, width=120, anchor=tk.CENTER)
            else:
                self.tree.column(col, width=200, anchor=tk.CENTER)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # إطار الملخص
        summary_frame = tk.Frame(self.window, bg=COLORS['background'])
        summary_frame.pack(fill=tk.X, padx=10, pady=5)

        self.summary_label = tk.Label(summary_frame, text="",
                                     font=(FONT_FAMILY, 12, 'bold'),
                                     bg=COLORS['background'])
        self.summary_label.pack()

        # زر الإغلاق
        close_button = tk.Button(self.window, text="إغلاق",
                               command=self.window.destroy,
                               font=(FONT_FAMILY, 12, 'bold'),
                               bg=COLORS['warning'], fg='white',
                               width=15)
        close_button.pack(pady=10)

    def load_payments(self):
        """تحميل المدفوعات في الجدول"""
        total_amount = 0

        for payment in self.payments:
            self.tree.insert('', tk.END, values=(
                payment['id'],
                f"{payment['amount']:.2f}",
                payment['payment_type'],
                payment['payment_method'],
                payment['payment_date'],
                payment['notes'] or ''
            ))

            if payment['payment_type'] == 'استلام':
                total_amount += payment['amount']
            else:
                total_amount -= payment['amount']

        # تحديث الملخص
        self.summary_label.config(
            text=f"إجمالي المدفوعات: {len(self.payments)} | المبلغ الإجمالي: {total_amount:.2f} {CURRENCY_SYMBOL}"
        )

class CustomerDetailsWindow:
    """نافذة تفاصيل الزبون الشاملة"""

    def __init__(self, parent, customer_id, customer_name, db):
        self.parent = parent
        self.customer_id = customer_id
        self.customer_name = customer_name
        self.db = db

        self.window = tk.Toplevel(parent)
        self.window.title(f"تفاصيل الزبون: {customer_name}")
        self.window.geometry("1200x800")
        self.window.configure(bg=COLORS['background'])
        self.window.transient(parent)

        self.create_interface()
        self.load_customer_data()
        self.center_window()

    def center_window(self):
        """وضع النافذة في المنتصف"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def create_interface(self):
        """إنشاء واجهة تفاصيل الزبون"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg=COLORS['secondary'], height=60)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text=f"ملف الزبون: {self.customer_name}",
                              font=(FONT_FAMILY, 18, 'bold'),
                              bg=COLORS['secondary'], fg='white')
        title_label.pack(expand=True)

        # إطار المعلومات الأساسية
        info_frame = tk.LabelFrame(self.window, text="المعلومات الأساسية",
                                  font=(FONT_FAMILY, 14, 'bold'),
                                  bg=COLORS['background'], fg=COLORS['text'])
        info_frame.pack(fill=tk.X, padx=10, pady=5)

        self.create_basic_info(info_frame)

        # إطار الإحصائيات
        stats_frame = tk.LabelFrame(self.window, text="الإحصائيات",
                                   font=(FONT_FAMILY, 14, 'bold'),
                                   bg=COLORS['background'], fg=COLORS['text'])
        stats_frame.pack(fill=tk.X, padx=10, pady=5)

        self.create_statistics(stats_frame)

        # إطار التبويبات
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # تبويب المبيعات
        sales_frame = tk.Frame(notebook, bg=COLORS['background'])
        notebook.add(sales_frame, text="المبيعات والمواد المأخوذة")
        self.create_sales_tab(sales_frame)

        # تبويب المدفوعات
        payments_frame = tk.Frame(notebook, bg=COLORS['background'])
        notebook.add(payments_frame, text="المدفوعات")
        self.create_payments_tab(payments_frame)

        # تبويب الديون
        debts_frame = tk.Frame(notebook, bg=COLORS['background'])
        notebook.add(debts_frame, text="الديون والمستحقات")
        self.create_debts_tab(debts_frame)

        # أزرار العمليات
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Button(buttons_frame, text="إضافة دفعة",
                 command=self.add_payment,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['success'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="طباعة كشف حساب",
                 command=self.print_statement,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['info'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="تحديث البيانات",
                 command=self.refresh_data,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['primary'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="إغلاق",
                 command=self.window.destroy,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['warning'], fg='white',
                 width=15).pack(side=tk.RIGHT, padx=5)

    def create_basic_info(self, parent):
        """إنشاء قسم المعلومات الأساسية"""
        # سيتم ملؤها بالبيانات لاحقاً
        self.info_labels = {}

        info_data = [
            ("الاسم:", "name"),
            ("الهاتف:", "phone"),
            ("العنوان:", "address"),
            ("البريد الإلكتروني:", "email"),
            ("الحد الائتماني:", "credit_limit"),
            ("الدين الحالي:", "current_debt")
        ]

        for i, (label_text, key) in enumerate(info_data):
            row = i // 3
            col = (i % 3) * 2

            tk.Label(parent, text=label_text,
                    font=(FONT_FAMILY, 12, 'bold'),
                    bg=COLORS['background']).grid(row=row, column=col, sticky=tk.W, padx=10, pady=5)

            value_label = tk.Label(parent, text="",
                                  font=(FONT_FAMILY, 12),
                                  bg=COLORS['background'])
            value_label.grid(row=row, column=col+1, sticky=tk.W, padx=10, pady=5)
            self.info_labels[key] = value_label

    def create_statistics(self, parent):
        """إنشاء قسم الإحصائيات"""
        self.stats_labels = {}

        stats_data = [
            ("إجمالي المبيعات:", "total_sales"),
            ("عدد المبيعات:", "sales_count"),
            ("إجمالي المدفوعات:", "total_payments"),
            ("آخر عملية شراء:", "last_purchase"),
            ("آخر دفعة:", "last_payment"),
            ("الرصيد المتاح:", "available_credit")
        ]

        for i, (label_text, key) in enumerate(stats_data):
            row = i // 3
            col = (i % 3) * 2

            tk.Label(parent, text=label_text,
                    font=(FONT_FAMILY, 12, 'bold'),
                    bg=COLORS['background']).grid(row=row, column=col, sticky=tk.W, padx=10, pady=5)

            value_label = tk.Label(parent, text="",
                                  font=(FONT_FAMILY, 12),
                                  bg=COLORS['background'])
            value_label.grid(row=row, column=col+1, sticky=tk.W, padx=10, pady=5)
            self.stats_labels[key] = value_label

    def create_sales_tab(self, parent):
        """إنشاء تبويب المبيعات والمواد المأخوذة"""
        # جدول المبيعات
        columns = ('sale_id', 'sale_date', 'total_amount', 'paid_amount', 'remaining_amount', 'payment_method')

        self.sales_tree = ttk.Treeview(parent, columns=columns, show='headings', height=12)

        headers = {
            'sale_id': 'رقم البيع',
            'sale_date': 'التاريخ',
            'total_amount': 'المبلغ الإجمالي',
            'paid_amount': 'المدفوع',
            'remaining_amount': 'المتبقي',
            'payment_method': 'طريقة الدفع'
        }

        for col in columns:
            self.sales_tree.heading(col, text=headers[col])
            self.sales_tree.column(col, width=120, anchor=tk.CENTER)

        # شريط التمرير للمبيعات
        sales_scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.sales_tree.yview)
        self.sales_tree.configure(yscrollcommand=sales_scrollbar.set)

        self.sales_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sales_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # ربط النقر المزدوج لعرض تفاصيل البيع
        self.sales_tree.bind('<Double-1>', self.view_sale_details)

    def create_payments_tab(self, parent):
        """إنشاء تبويب المدفوعات"""
        columns = ('payment_id', 'payment_date', 'amount', 'payment_type', 'payment_method', 'notes')

        self.payments_tree = ttk.Treeview(parent, columns=columns, show='headings', height=12)

        headers = {
            'payment_id': 'رقم الدفعة',
            'payment_date': 'التاريخ',
            'amount': 'المبلغ',
            'payment_type': 'النوع',
            'payment_method': 'طريقة الدفع',
            'notes': 'ملاحظات'
        }

        for col in columns:
            self.payments_tree.heading(col, text=headers[col])
            if col == 'notes':
                self.payments_tree.column(col, width=200, anchor=tk.W)
            else:
                self.payments_tree.column(col, width=120, anchor=tk.CENTER)

        # شريط التمرير للمدفوعات
        payments_scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.payments_tree.yview)
        self.payments_tree.configure(yscrollcommand=payments_scrollbar.set)

        self.payments_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        payments_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_debts_tab(self, parent):
        """إنشاء تبويب الديون والمستحقات"""
        # إطار معلومات الديون
        debt_info_frame = tk.Frame(parent, bg=COLORS['background'])
        debt_info_frame.pack(fill=tk.X, padx=10, pady=5)

        # معلومات الدين
        self.debt_labels = {}
        debt_data = [
            ("الدين الحالي:", "current_debt"),
            ("الحد الائتماني:", "credit_limit"),
            ("الرصيد المتاح:", "available_credit"),
            ("أقدم دين:", "oldest_debt"),
            ("عدد الفواتير المعلقة:", "pending_invoices")
        ]

        for i, (label_text, key) in enumerate(debt_data):
            row = i // 2
            col = (i % 2) * 2

            tk.Label(debt_info_frame, text=label_text,
                    font=(FONT_FAMILY, 12, 'bold'),
                    bg=COLORS['background']).grid(row=row, column=col, sticky=tk.W, padx=10, pady=5)

            value_label = tk.Label(debt_info_frame, text="",
                                  font=(FONT_FAMILY, 12),
                                  bg=COLORS['background'])
            value_label.grid(row=row, column=col+1, sticky=tk.W, padx=10, pady=5)
            self.debt_labels[key] = value_label

        # جدول الفواتير المعلقة
        tk.Label(parent, text="الفواتير المعلقة:",
                font=(FONT_FAMILY, 14, 'bold'),
                bg=COLORS['background']).pack(anchor=tk.W, padx=10, pady=(10, 5))

        pending_frame = tk.Frame(parent, bg=COLORS['background'])
        pending_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        columns = ('sale_id', 'sale_date', 'total_amount', 'paid_amount', 'remaining_amount', 'days_overdue')

        self.pending_tree = ttk.Treeview(pending_frame, columns=columns, show='headings', height=8)

        headers = {
            'sale_id': 'رقم الفاتورة',
            'sale_date': 'تاريخ الفاتورة',
            'total_amount': 'المبلغ الإجمالي',
            'paid_amount': 'المدفوع',
            'remaining_amount': 'المتبقي',
            'days_overdue': 'أيام التأخير'
        }

        for col in columns:
            self.pending_tree.heading(col, text=headers[col])
            self.pending_tree.column(col, width=120, anchor=tk.CENTER)

        # شريط التمرير للفواتير المعلقة
        pending_scrollbar = ttk.Scrollbar(pending_frame, orient=tk.VERTICAL, command=self.pending_tree.yview)
        self.pending_tree.configure(yscrollcommand=pending_scrollbar.set)

        self.pending_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        pending_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def load_customer_data(self):
        """تحميل بيانات الزبون"""
        try:
            # تحميل المعلومات الأساسية
            customer = self.db.get_customer_by_id(self.customer_id)
            if customer:
                self.info_labels['name'].config(text=customer['name'])
                self.info_labels['phone'].config(text=customer['phone'] or 'غير محدد')
                self.info_labels['address'].config(text=customer['address'] or 'غير محدد')
                self.info_labels['email'].config(text=customer['email'] or 'غير محدد')
                self.info_labels['credit_limit'].config(text=f"{customer['credit_limit']:.2f} {CURRENCY_SYMBOL}")
                self.info_labels['current_debt'].config(text=f"{customer['current_debt']:.2f} {CURRENCY_SYMBOL}")

            # تحميل الإحصائيات
            self.load_statistics()

            # تحميل المبيعات
            self.load_sales()

            # تحميل المدفوعات
            self.load_payments()

            # تحميل الديون
            self.load_debts()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات الزبون: {str(e)}")

    def load_statistics(self):
        """تحميل الإحصائيات"""
        try:
            # إحصائيات المبيعات
            conn = self.db.db_manager.get_connection()
            cursor = conn.cursor()

            # إجمالي المبيعات وعددها
            cursor.execute('''
                SELECT COUNT(*) as sales_count,
                       COALESCE(SUM(total_amount), 0) as total_sales,
                       MAX(sale_date) as last_purchase
                FROM sales
                WHERE customer_id = ?
            ''', (self.customer_id,))

            sales_stats = dict(cursor.fetchone())

            # إجمالي المدفوعات وآخر دفعة
            cursor.execute('''
                SELECT COALESCE(SUM(amount), 0) as total_payments,
                       MAX(payment_date) as last_payment
                FROM payments
                WHERE customer_id = ? AND payment_type = 'استلام'
            ''', (self.customer_id,))

            payment_stats = dict(cursor.fetchone())

            conn.close()

            # تحديث التسميات
            self.stats_labels['total_sales'].config(text=f"{sales_stats['total_sales']:.2f} {CURRENCY_SYMBOL}")
            self.stats_labels['sales_count'].config(text=str(sales_stats['sales_count']))
            self.stats_labels['total_payments'].config(text=f"{payment_stats['total_payments']:.2f} {CURRENCY_SYMBOL}")
            self.stats_labels['last_purchase'].config(text=sales_stats['last_purchase'] or 'لا يوجد')
            self.stats_labels['last_payment'].config(text=payment_stats['last_payment'] or 'لا يوجد')

            # الرصيد المتاح
            customer = self.db.get_customer_by_id(self.customer_id)
            available_credit = customer['credit_limit'] - customer['current_debt']
            self.stats_labels['available_credit'].config(text=f"{available_credit:.2f} {CURRENCY_SYMBOL}")

        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {str(e)}")

    def load_sales(self):
        """تحميل المبيعات"""
        try:
            # مسح البيانات الحالية
            for item in self.sales_tree.get_children():
                self.sales_tree.delete(item)

            # تحميل مبيعات الزبون
            conn = self.db.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, sale_date, total_amount, paid_amount, remaining_amount, payment_method
                FROM sales
                WHERE customer_id = ?
                ORDER BY sale_date DESC
            ''', (self.customer_id,))

            sales = cursor.fetchall()
            conn.close()

            for sale in sales:
                # تلوين الصفوف حسب حالة الدفع
                tags = []
                if sale[4] > 0:  # remaining_amount
                    tags.append('has_debt')

                self.sales_tree.insert('', tk.END, values=(
                    sale[0],  # sale_id
                    sale[1],  # sale_date
                    f"{sale[2]:.2f}",  # total_amount
                    f"{sale[3]:.2f}",  # paid_amount
                    f"{sale[4]:.2f}",  # remaining_amount
                    sale[5]   # payment_method
                ), tags=tags)

            # تعيين ألوان للصفوف
            self.sales_tree.tag_configure('has_debt', background='#fff3cd')

        except Exception as e:
            print(f"خطأ في تحميل المبيعات: {str(e)}")

    def load_payments(self):
        """تحميل المدفوعات"""
        try:
            # مسح البيانات الحالية
            for item in self.payments_tree.get_children():
                self.payments_tree.delete(item)

            # تحميل مدفوعات الزبون
            payments = self.db.get_customer_payments(self.customer_id)

            for payment in payments:
                self.payments_tree.insert('', tk.END, values=(
                    payment['id'],
                    payment['payment_date'],
                    f"{payment['amount']:.2f}",
                    payment['payment_type'],
                    payment['payment_method'],
                    payment['notes'] or ''
                ))

        except Exception as e:
            print(f"خطأ في تحميل المدفوعات: {str(e)}")

    def load_debts(self):
        """تحميل معلومات الديون"""
        try:
            customer = self.db.get_customer_by_id(self.customer_id)

            # تحديث معلومات الدين
            self.debt_labels['current_debt'].config(text=f"{customer['current_debt']:.2f} {CURRENCY_SYMBOL}")
            self.debt_labels['credit_limit'].config(text=f"{customer['credit_limit']:.2f} {CURRENCY_SYMBOL}")

            available_credit = customer['credit_limit'] - customer['current_debt']
            self.debt_labels['available_credit'].config(text=f"{available_credit:.2f} {CURRENCY_SYMBOL}")

            # تحميل الفواتير المعلقة
            self.load_pending_invoices()

        except Exception as e:
            print(f"خطأ في تحميل معلومات الديون: {str(e)}")

    def load_pending_invoices(self):
        """تحميل الفواتير المعلقة"""
        try:
            # مسح البيانات الحالية
            for item in self.pending_tree.get_children():
                self.pending_tree.delete(item)

            # تحميل الفواتير المعلقة
            conn = self.db.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, sale_date, total_amount, paid_amount, remaining_amount,
                       julianday('now') - julianday(sale_date) as days_overdue
                FROM sales
                WHERE customer_id = ? AND remaining_amount > 0
                ORDER BY sale_date ASC
            ''', (self.customer_id,))

            pending_invoices = cursor.fetchall()
            conn.close()

            for invoice in pending_invoices:
                # تلوين حسب أيام التأخير
                tags = []
                days_overdue = int(invoice[5])
                if days_overdue > 30:
                    tags.append('overdue_critical')
                elif days_overdue > 7:
                    tags.append('overdue_warning')

                self.pending_tree.insert('', tk.END, values=(
                    invoice[0],  # sale_id
                    invoice[1],  # sale_date
                    f"{invoice[2]:.2f}",  # total_amount
                    f"{invoice[3]:.2f}",  # paid_amount
                    f"{invoice[4]:.2f}",  # remaining_amount
                    f"{days_overdue} يوم"  # days_overdue
                ), tags=tags)

            # تعيين ألوان للصفوف
            self.pending_tree.tag_configure('overdue_warning', background='#fff3cd')
            self.pending_tree.tag_configure('overdue_critical', background='#f8d7da')

            # تحديث عدد الفواتير المعلقة
            self.debt_labels['pending_invoices'].config(text=str(len(pending_invoices)))

            # تحديث أقدم دين
            if pending_invoices:
                oldest_date = pending_invoices[-1][1]  # آخر عنصر (الأقدم)
                self.debt_labels['oldest_debt'].config(text=oldest_date)
            else:
                self.debt_labels['oldest_debt'].config(text='لا يوجد')

        except Exception as e:
            print(f"خطأ في تحميل الفواتير المعلقة: {str(e)}")

    def view_sale_details(self, event):
        """عرض تفاصيل البيع المحدد"""
        selected_item = self.sales_tree.selection()
        if not selected_item:
            return

        # الحصول على رقم البيع
        item_values = self.sales_tree.item(selected_item[0])['values']
        sale_id = item_values[0]

        try:
            # الحصول على تفاصيل البيع
            sale_items = self.db.get_sale_items(sale_id)

            if sale_items:
                from gui.sales import SaleDetailsWindow
                SaleDetailsWindow(self.window, sale_id, sale_items)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل تفاصيل البيع: {str(e)}")

    def add_payment(self):
        """إضافة دفعة جديدة"""
        customer = self.db.get_customer_by_id(self.customer_id)
        if customer['current_debt'] <= 0:
            messagebox.showinfo("معلومة", "هذا الزبون ليس لديه ديون")
            return

        # طلب مبلغ الدفعة
        from tkinter import simpledialog
        amount = simpledialog.askfloat(
            "إضافة دفعة",
            f"الزبون: {self.customer_name}\nالدين الحالي: {customer['current_debt']:.2f} {CURRENCY_SYMBOL}\n\nأدخل مبلغ الدفعة:",
            minvalue=0.01,
            maxvalue=customer['current_debt']
        )

        if amount:
            try:
                self.db.add_payment(
                    customer_id=self.customer_id,
                    amount=amount,
                    payment_type='استلام',
                    payment_method='نقدي',
                    notes=f"دفعة من الزبون {self.customer_name}"
                )
                messagebox.showinfo("نجح", f"تم تسجيل الدفعة بمبلغ {amount:.2f} {CURRENCY_SYMBOL}")
                self.refresh_data()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تسجيل الدفعة: {str(e)}")

    def print_statement(self):
        """طباعة كشف حساب الزبون"""
        try:
            from utils.reports import CustomerStatementReport
            report = CustomerStatementReport(self.db)
            report.generate_customer_statement(self.customer_id, self.customer_name)
            messagebox.showinfo("نجح", "تم إنشاء كشف الحساب بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء كشف الحساب: {str(e)}")

    def refresh_data(self):
        """تحديث جميع البيانات"""
        self.load_customer_data()

if __name__ == "__main__":
    # اختبار النافذة
    app = CustomersWindow()
    app.window.mainloop()
