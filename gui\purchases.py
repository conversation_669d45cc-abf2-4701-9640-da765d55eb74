# -*- coding: utf-8 -*-
"""
نافذة إدارة المشتريات لبرنامج محاسبة الصيدلية الزراعية
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sys
import os
from datetime import datetime

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import *

class PurchasesWindow:
    """نافذة إدارة المشتريات"""
    
    def __init__(self, parent=None, db=None):
        self.parent = parent
        self.db = db
        
        if parent:
            self.window = tk.Toplevel(parent)
            self.window.transient(parent)
        else:
            self.window = tk.Tk()
        
        self.window.title("إدارة المشتريات")
        self.window.geometry("1200x700")
        self.window.configure(bg=COLORS['background'])
        
        self.create_interface()
        self.load_purchases()
        self.center_window()
        
    def center_window(self):
        """وضع النافذة في المنتصف"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_interface(self):
        """إنشاء واجهة إدارة المشتريات"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg=COLORS['primary'], height=60)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="إدارة المشتريات",
                              font=(FONT_FAMILY, 18, 'bold'),
                              bg=COLORS['primary'], fg='white')
        title_label.pack(expand=True)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Button(buttons_frame, text="🛒 مشترى جديد",
                 command=self.new_purchase,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['success'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="📝 تفاصيل المشترى",
                 command=self.view_purchase_details,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['info'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="💰 إضافة دفعة",
                 command=self.add_payment,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['warning'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="🔄 تحديث",
                 command=self.load_purchases,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['secondary'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        # إطار البحث
        search_frame = tk.Frame(self.window, bg=COLORS['background'])
        search_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(search_frame, text="البحث:",
                font=(FONT_FAMILY, 12, 'bold'),
                bg=COLORS['background']).pack(side=tk.LEFT, padx=5)
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.filter_purchases)
        search_entry = tk.Entry(search_frame, textvariable=self.search_var,
                               font=(FONT_FAMILY, 12), width=30)
        search_entry.pack(side=tk.LEFT, padx=5)
        
        # إطار الجدول
        table_frame = tk.Frame(self.window, bg=COLORS['background'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # إنشاء جدول المشتريات
        columns = ('id', 'supplier_name', 'purchase_date', 'total_amount', 'paid_amount', 'remaining_amount', 'status')
        
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة
        headers = {
            'id': 'رقم المشترى',
            'supplier_name': 'المورد',
            'purchase_date': 'التاريخ',
            'total_amount': 'المبلغ الإجمالي',
            'paid_amount': 'المدفوع',
            'remaining_amount': 'المتبقي',
            'status': 'الحالة'
        }
        
        for col in columns:
            self.tree.heading(col, text=headers[col])
            if col == 'supplier_name':
                self.tree.column(col, width=200, anchor=tk.W)
            elif col == 'status':
                self.tree.column(col, width=100, anchor=tk.CENTER)
            else:
                self.tree.column(col, width=120, anchor=tk.CENTER)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # إطار الملخص
        summary_frame = tk.Frame(self.window, bg=COLORS['background'])
        summary_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.summary_label = tk.Label(summary_frame, text="",
                                     font=(FONT_FAMILY, 12, 'bold'),
                                     bg=COLORS['background'])
        self.summary_label.pack()
        
        # ربط النقر المزدوج
        self.tree.bind('<Double-1>', lambda e: self.view_purchase_details())
        
    def load_purchases(self):
        """تحميل المشتريات"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل المشتريات من قاعدة البيانات
            conn = self.db.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT p.id, s.name as supplier_name, p.purchase_date, 
                       p.total_amount, p.paid_amount, p.remaining_amount,
                       CASE 
                           WHEN p.remaining_amount = 0 THEN 'مدفوع'
                           WHEN p.paid_amount = 0 THEN 'غير مدفوع'
                           ELSE 'مدفوع جزئياً'
                       END as status
                FROM purchases p
                LEFT JOIN suppliers s ON p.supplier_id = s.id
                ORDER BY p.purchase_date DESC
            ''')
            
            purchases = cursor.fetchall()
            conn.close()
            
            total_purchases = 0
            total_paid = 0
            total_remaining = 0
            
            for purchase in purchases:
                # تلوين الصفوف حسب حالة الدفع
                tags = []
                if purchase[5] > 0:  # remaining_amount
                    if purchase[4] == 0:  # paid_amount
                        tags.append('unpaid')
                    else:
                        tags.append('partial')
                else:
                    tags.append('paid')
                
                self.tree.insert('', tk.END, values=(
                    purchase[0],  # id
                    purchase[1],  # supplier_name
                    purchase[2],  # purchase_date
                    f"{purchase[3]:.2f}",  # total_amount
                    f"{purchase[4]:.2f}",  # paid_amount
                    f"{purchase[5]:.2f}",  # remaining_amount
                    purchase[6]   # status
                ), tags=tags)
                
                total_purchases += purchase[3]
                total_paid += purchase[4]
                total_remaining += purchase[5]
            
            # تعيين ألوان للصفوف
            self.tree.tag_configure('paid', background='#d4edda')
            self.tree.tag_configure('partial', background='#fff3cd')
            self.tree.tag_configure('unpaid', background='#f8d7da')
            
            # تحديث الملخص
            self.summary_label.config(
                text=f"إجمالي المشتريات: {len(purchases)} | المبلغ الإجمالي: {total_purchases:.2f} {CURRENCY_SYMBOL} | المدفوع: {total_paid:.2f} {CURRENCY_SYMBOL} | المتبقي: {total_remaining:.2f} {CURRENCY_SYMBOL}"
            )
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المشتريات: {str(e)}")
    
    def filter_purchases(self, *args):
        """تصفية المشتريات حسب البحث"""
        search_term = self.search_var.get().lower()
        
        # إخفاء جميع العناصر
        for item in self.tree.get_children():
            self.tree.detach(item)
        
        # إظهار العناصر المطابقة فقط
        for item in self.tree.get_children():
            values = self.tree.item(item)['values']
            if any(search_term in str(value).lower() for value in values):
                self.tree.reattach(item, '', 'end')
    
    def new_purchase(self):
        """إنشاء مشترى جديد"""
        try:
            NewPurchaseWindow(self.window, self.db, self.load_purchases)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة المشترى الجديد: {str(e)}")
    
    def view_purchase_details(self):
        """عرض تفاصيل المشترى"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار مشترى لعرض تفاصيله")
            return
        
        # الحصول على رقم المشترى
        item_values = self.tree.item(selected_item[0])['values']
        purchase_id = item_values[0]
        
        try:
            PurchaseDetailsWindow(self.window, purchase_id, self.db)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض تفاصيل المشترى: {str(e)}")
    
    def add_payment(self):
        """إضافة دفعة لمشترى"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار مشترى لإضافة دفعة")
            return
        
        # الحصول على بيانات المشترى
        item_values = self.tree.item(selected_item[0])['values']
        purchase_id = item_values[0]
        supplier_name = item_values[1]
        remaining_amount = float(item_values[5])
        
        if remaining_amount <= 0:
            messagebox.showinfo("معلومة", "هذا المشترى مدفوع بالكامل")
            return
        
        # طلب مبلغ الدفعة
        amount = simpledialog.askfloat(
            "إضافة دفعة",
            f"المورد: {supplier_name}\nالمبلغ المتبقي: {remaining_amount:.2f} {CURRENCY_SYMBOL}\n\nأدخل مبلغ الدفعة:",
            minvalue=0.01,
            maxvalue=remaining_amount
        )
        
        if amount:
            try:
                # إضافة الدفعة
                conn = self.db.db_manager.get_connection()
                cursor = conn.cursor()
                
                # الحصول على معرف المورد
                cursor.execute('SELECT supplier_id FROM purchases WHERE id = ?', (purchase_id,))
                supplier_id = cursor.fetchone()[0]
                
                # إضافة الدفعة
                cursor.execute('''
                    INSERT INTO payments (supplier_id, amount, payment_type, payment_method, notes, payment_date)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (supplier_id, amount, 'دفع', 'نقدي', f'دفعة للمشترى رقم {purchase_id}', datetime.now().strftime('%Y-%m-%d')))
                
                # تحديث المشترى
                cursor.execute('''
                    UPDATE purchases 
                    SET paid_amount = paid_amount + ?, 
                        remaining_amount = remaining_amount - ?
                    WHERE id = ?
                ''', (amount, amount, purchase_id))
                
                conn.commit()
                conn.close()
                
                messagebox.showinfo("نجح", f"تم تسجيل الدفعة بمبلغ {amount:.2f} {CURRENCY_SYMBOL}")
                self.load_purchases()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تسجيل الدفعة: {str(e)}")

class NewPurchaseWindow:
    """نافذة إنشاء مشترى جديد"""

    def __init__(self, parent, db, refresh_callback=None):
        self.parent = parent
        self.db = db
        self.refresh_callback = refresh_callback

        self.window = tk.Toplevel(parent)
        self.window.title("مشترى جديد")
        self.window.geometry("1000x700")
        self.window.configure(bg=COLORS['background'])
        self.window.transient(parent)
        self.window.grab_set()

        # متغيرات البيانات
        self.purchase_items = []
        self.suppliers_dict = {}
        self.products_dict = {}

        self.setup_window()
        self.create_interface()
        self.load_suppliers()
        self.load_products()
        self.setup_keyboard_shortcuts()

    def setup_window(self):
        """إعداد النافذة"""
        self.center_window()

    def center_window(self):
        """وضع النافذة في المنتصف"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def create_interface(self):
        """إنشاء واجهة المشترى الجديد"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg=COLORS['success'], height=60)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="🛒 مشترى جديد",
                              font=(FONT_FAMILY, 18, 'bold'),
                              bg=COLORS['success'], fg='white')
        title_label.pack(expand=True)

        # إطار معلومات المشترى
        info_frame = tk.LabelFrame(self.window, text="معلومات المشترى",
                                  font=(FONT_FAMILY, 14, 'bold'),
                                  bg=COLORS['background'])
        info_frame.pack(fill=tk.X, padx=10, pady=5)

        # اختيار المورد
        supplier_frame = tk.Frame(info_frame, bg=COLORS['background'])
        supplier_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(supplier_frame, text="المورد:",
                font=(FONT_FAMILY, 12, 'bold'),
                bg=COLORS['background']).pack(side=tk.LEFT, padx=5)

        self.supplier_var = tk.StringVar()
        self.supplier_combo = ttk.Combobox(supplier_frame, textvariable=self.supplier_var,
                                          font=(FONT_FAMILY, 12), width=30, state='readonly')
        self.supplier_combo.pack(side=tk.LEFT, padx=5)

        # تاريخ المشترى
        date_frame = tk.Frame(info_frame, bg=COLORS['background'])
        date_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(date_frame, text="التاريخ:",
                font=(FONT_FAMILY, 12, 'bold'),
                bg=COLORS['background']).pack(side=tk.LEFT, padx=5)

        self.date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        date_entry = tk.Entry(date_frame, textvariable=self.date_var,
                             font=(FONT_FAMILY, 12), width=15)
        date_entry.pack(side=tk.LEFT, padx=5)

        # إطار إضافة المنتجات
        add_frame = tk.LabelFrame(self.window, text="إضافة منتجات",
                                 font=(FONT_FAMILY, 14, 'bold'),
                                 bg=COLORS['background'])
        add_frame.pack(fill=tk.X, padx=10, pady=5)

        # صف إضافة المنتج
        product_row = tk.Frame(add_frame, bg=COLORS['background'])
        product_row.pack(fill=tk.X, padx=10, pady=5)

        # اختيار المنتج
        tk.Label(product_row, text="المنتج:",
                font=(FONT_FAMILY, 12, 'bold'),
                bg=COLORS['background']).grid(row=0, column=0, padx=5, sticky=tk.W)

        self.product_var = tk.StringVar()
        self.product_combo = ttk.Combobox(product_row, textvariable=self.product_var,
                                         font=(FONT_FAMILY, 12), width=25)
        self.product_combo.grid(row=0, column=1, padx=5)
        self.product_combo.bind('<<ComboboxSelected>>', self.on_product_selected)

        # الكمية
        tk.Label(product_row, text="الكمية:",
                font=(FONT_FAMILY, 12, 'bold'),
                bg=COLORS['background']).grid(row=0, column=2, padx=5, sticky=tk.W)

        self.quantity_var = tk.StringVar(value='1')
        quantity_entry = tk.Entry(product_row, textvariable=self.quantity_var,
                                 font=(FONT_FAMILY, 12), width=10)
        quantity_entry.grid(row=0, column=3, padx=5)

        # سعر الشراء
        tk.Label(product_row, text="سعر الشراء:",
                font=(FONT_FAMILY, 12, 'bold'),
                bg=COLORS['background']).grid(row=0, column=4, padx=5, sticky=tk.W)

        self.purchase_price_var = tk.StringVar()
        self.purchase_price_entry = tk.Entry(product_row, textvariable=self.purchase_price_var,
                                            font=(FONT_FAMILY, 12), width=10)
        self.purchase_price_entry.grid(row=0, column=5, padx=5)

        # زر الإضافة
        tk.Button(product_row, text="➕ إضافة",
                 command=self.add_item,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['success'], fg='white',
                 width=10).grid(row=0, column=6, padx=10)

        # إطار جدول المنتجات
        table_frame = tk.LabelFrame(self.window, text="منتجات المشترى",
                                   font=(FONT_FAMILY, 14, 'bold'),
                                   bg=COLORS['background'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # جدول المنتجات
        columns = ('product_name', 'quantity', 'unit', 'purchase_price', 'total_price')

        self.items_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=10)

        headers = {
            'product_name': 'المنتج',
            'quantity': 'الكمية',
            'unit': 'الوحدة',
            'purchase_price': 'سعر الشراء',
            'total_price': 'الإجمالي'
        }

        for col in columns:
            self.items_tree.heading(col, text=headers[col])
            if col == 'product_name':
                self.items_tree.column(col, width=250, anchor=tk.W)
            else:
                self.items_tree.column(col, width=120, anchor=tk.CENTER)

        # شريط التمرير للجدول
        items_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=items_scrollbar.set)

        self.items_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        items_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # إطار الإجمالي والأزرار
        bottom_frame = tk.Frame(self.window, bg=COLORS['background'])
        bottom_frame.pack(fill=tk.X, padx=10, pady=10)

        # الإجمالي
        total_frame = tk.Frame(bottom_frame, bg=COLORS['background'])
        total_frame.pack(side=tk.LEFT)

        tk.Label(total_frame, text="الإجمالي:",
                font=(FONT_FAMILY, 14, 'bold'),
                bg=COLORS['background']).pack(side=tk.LEFT, padx=5)

        self.total_label = tk.Label(total_frame, text="0.00",
                                   font=(FONT_FAMILY, 14, 'bold'),
                                   bg=COLORS['background'], fg=COLORS['success'])
        self.total_label.pack(side=tk.LEFT, padx=5)

        tk.Label(total_frame, text=CURRENCY_SYMBOL,
                font=(FONT_FAMILY, 14, 'bold'),
                bg=COLORS['background']).pack(side=tk.LEFT)

        # الأزرار
        buttons_frame = tk.Frame(bottom_frame, bg=COLORS['background'])
        buttons_frame.pack(side=tk.RIGHT)

        tk.Button(buttons_frame, text="🗑️ حذف منتج",
                 command=self.remove_item,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['danger'], fg='white',
                 width=12).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="💾 حفظ المشترى",
                 command=self.save_purchase,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['primary'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="❌ إلغاء",
                 command=self.window.destroy,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['warning'], fg='white',
                 width=10).pack(side=tk.LEFT, padx=5)

        # التركيز على حقل المورد
        self.supplier_combo.focus()

    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        self.window.bind('<F1>', lambda e: self.add_item())
        self.window.bind('<F2>', lambda e: self.save_purchase())
        self.window.bind('<F3>', lambda e: self.remove_item())
        self.window.bind('<Escape>', lambda e: self.window.destroy())

        # تحديث عنوان النافذة
        self.window.title("مشترى جديد - F1:إضافة | F2:حفظ | F3:حذف | Esc:إغلاق")

    def load_suppliers(self):
        """تحميل الموردين"""
        try:
            suppliers = self.db.get_all_suppliers()
            supplier_names = [supplier['name'] for supplier in suppliers]

            self.supplier_combo['values'] = supplier_names

            # إنشاء قاموس للموردين
            self.suppliers_dict = {supplier['name']: supplier for supplier in suppliers}

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الموردين: {str(e)}")

    def load_products(self):
        """تحميل المنتجات"""
        try:
            products = self.db.get_all_products()
            product_names = [f"{product['name']} ({product['unit']})" for product in products]

            self.product_combo['values'] = product_names

            # إنشاء قاموس للمنتجات
            self.products_dict = {f"{product['name']} ({product['unit']})": product for product in products}

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المنتجات: {str(e)}")

    def on_product_selected(self, event):
        """عند اختيار منتج"""
        selected_product = self.product_var.get()
        if selected_product in self.products_dict:
            product = self.products_dict[selected_product]
            # تعيين سعر الشراء الافتراضي
            self.purchase_price_var.set(str(product['purchase_price']))

    def add_item(self):
        """إضافة منتج للمشترى"""
        try:
            # التحقق من البيانات
            selected_product = self.product_var.get()
            if not selected_product or selected_product not in self.products_dict:
                messagebox.showwarning("تحذير", "يرجى اختيار منتج من القائمة")
                return

            try:
                quantity = int(self.quantity_var.get())
                if quantity <= 0:
                    raise ValueError()
            except ValueError:
                messagebox.showwarning("تحذير", "يرجى إدخال كمية صحيحة أكبر من صفر")
                return

            try:
                purchase_price = float(self.purchase_price_var.get())
                if purchase_price <= 0:
                    raise ValueError()
            except ValueError:
                messagebox.showwarning("تحذير", "يرجى إدخال سعر شراء صحيح أكبر من صفر")
                return

            product = self.products_dict[selected_product]

            # التحقق من وجود المنتج مسبقاً
            for existing_item in self.purchase_items:
                if existing_item['product_id'] == product['id']:
                    response = messagebox.askyesno(
                        "منتج موجود",
                        f"المنتج '{product['name']}' موجود مسبقاً في المشترى\n"
                        f"الكمية الحالية: {existing_item['quantity']}\n"
                        f"هل تريد زيادة الكمية بـ {quantity}؟"
                    )
                    if response:
                        # زيادة الكمية للمنتج الموجود
                        existing_item['quantity'] += quantity
                        existing_item['total_price'] = existing_item['quantity'] * existing_item['purchase_price']

                        # تحديث الجدول
                        self.refresh_items_table()
                        self.update_total()

                        # مسح الحقول
                        self.clear_item_fields()

                        messagebox.showinfo("تم", f"تم تحديث كمية '{product['name']}' إلى {existing_item['quantity']}")
                        return
                    else:
                        return

            # إضافة المنتج الجديد
            total_price = quantity * purchase_price

            item = {
                'product_id': product['id'],
                'product_name': product['name'],
                'quantity': quantity,
                'unit': product['unit'],
                'purchase_price': purchase_price,
                'total_price': total_price
            }

            self.purchase_items.append(item)

            # إضافة إلى الجدول
            self.items_tree.insert('', tk.END, values=(
                product['name'],
                quantity,
                product['unit'],
                f"{purchase_price:.2f}",
                f"{total_price:.2f}"
            ))

            # تحديث الإجمالي
            self.update_total()

            # مسح الحقول
            self.clear_item_fields()

            # رسالة نجاح
            messagebox.showinfo("تم", f"تم إضافة '{product['name']}' بكمية {quantity} {product['unit']}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إضافة المنتج: {str(e)}")

    def clear_item_fields(self):
        """مسح حقول إضافة المنتج"""
        self.product_var.set('')
        self.quantity_var.set('1')
        self.purchase_price_var.set('')
        # التركيز على حقل المنتج
        self.product_combo.focus()

    def refresh_items_table(self):
        """تحديث جدول المنتجات"""
        # مسح الجدول
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # إعادة إضافة المنتجات
        for item in self.purchase_items:
            self.items_tree.insert('', tk.END, values=(
                item['product_name'],
                item['quantity'],
                item['unit'],
                f"{item['purchase_price']:.2f}",
                f"{item['total_price']:.2f}"
            ))

    def update_total(self):
        """تحديث الإجمالي"""
        total = sum(item['total_price'] for item in self.purchase_items)
        self.total_label.config(text=f"{total:.2f}")

    def remove_item(self):
        """حذف منتج من المشترى"""
        selected_item = self.items_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للحذف من الجدول")
            return

        # الحصول على فهرس المنتج
        item_index = self.items_tree.index(selected_item[0])
        item_to_remove = self.purchase_items[item_index]

        # تأكيد الحذف
        response = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف المنتج:\n"
            f"'{item_to_remove['product_name']}'\n"
            f"الكمية: {item_to_remove['quantity']}\n"
            f"المبلغ: {item_to_remove['total_price']:.2f} {CURRENCY_SYMBOL}"
        )

        if response:
            # حذف من القائمة والجدول
            del self.purchase_items[item_index]
            self.items_tree.delete(selected_item[0])

            # تحديث الإجمالي
            self.update_total()

            messagebox.showinfo("تم", f"تم حذف '{item_to_remove['product_name']}' من المشترى")

    def save_purchase(self):
        """حفظ المشترى"""
        try:
            # التحقق من البيانات
            if not self.purchase_items:
                messagebox.showwarning("تحذير", "يرجى إضافة منتجات للمشترى")
                return

            supplier_name = self.supplier_var.get()
            if not supplier_name or supplier_name not in self.suppliers_dict:
                messagebox.showwarning("تحذير", "يرجى اختيار مورد")
                return

            # حساب الإجمالي
            total_amount = sum(item['total_price'] for item in self.purchase_items)
            supplier_id = self.suppliers_dict[supplier_name]['id']

            # عرض خيارات الدفع
            payment_choice = self.show_payment_options(supplier_name, total_amount)
            if payment_choice is None:
                return  # المستخدم ألغى العملية

            payment_type, paid_amount = payment_choice
            remaining_amount = total_amount - paid_amount

            # إنشاء المشترى
            conn = self.db.db_manager.get_connection()
            cursor = conn.cursor()

            # إضافة المشترى
            cursor.execute('''
                INSERT INTO purchases (supplier_id, purchase_date, total_amount, paid_amount, remaining_amount, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (supplier_id, self.date_var.get(), total_amount, paid_amount, remaining_amount, f"مشترى {payment_type}"))

            purchase_id = cursor.lastrowid

            # إضافة منتجات المشترى وتحديث المخزون
            for item in self.purchase_items:
                # إضافة منتج المشترى
                cursor.execute('''
                    INSERT INTO purchase_items (purchase_id, product_id, quantity, unit_price)
                    VALUES (?, ?, ?, ?)
                ''', (purchase_id, item['product_id'], item['quantity'], item['purchase_price']))

                # تحديث المخزون تلقائياً
                cursor.execute('''
                    UPDATE products
                    SET stock_quantity = stock_quantity + ?,
                        purchase_price = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (item['quantity'], item['purchase_price'], item['product_id']))

            # إضافة دفعة إذا كان هناك مبلغ مدفوع
            if paid_amount > 0:
                cursor.execute('''
                    INSERT INTO payments (supplier_id, amount, payment_type, payment_method, notes, payment_date)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (supplier_id, paid_amount, 'دفع', 'نقدي', f'دفعة للمشترى رقم {purchase_id}', self.date_var.get()))

            conn.commit()
            conn.close()

            # رسالة النجاح
            if payment_type == "بالتقسيط":
                message = f"تم حفظ المشترى برقم {purchase_id} بالتقسيط\nالمبلغ الإجمالي: {total_amount:.2f} {CURRENCY_SYMBOL}\nالمدفوع: {paid_amount:.2f} {CURRENCY_SYMBOL}\nالمتبقي: {remaining_amount:.2f} {CURRENCY_SYMBOL}\n\nتم إضافة المنتجات للمخزون تلقائياً"
                messagebox.showinfo("تم المشترى بالتقسيط", message)

                # فتح صفحة تفاصيل المورد
                self.open_supplier_details(supplier_id, supplier_name)
            else:
                messagebox.showinfo("نجح", f"تم حفظ المشترى برقم {purchase_id} بنجاح\nالمبلغ المدفوع: {paid_amount:.2f} {CURRENCY_SYMBOL}\n\nتم إضافة المنتجات للمخزون تلقائياً")

            # تحديث قائمة المشتريات
            if self.refresh_callback:
                self.refresh_callback()

            # إغلاق النافذة
            self.window.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ المشترى: {str(e)}")

    def show_payment_options(self, supplier_name, total_amount):
        """عرض خيارات الدفع للمورد"""
        # إنشاء نافذة خيارات الدفع
        payment_window = tk.Toplevel(self.window)
        payment_window.title("طريقة الدفع")
        payment_window.geometry("400x350")
        payment_window.configure(bg=COLORS['background'])
        payment_window.transient(self.window)
        payment_window.grab_set()

        # وضع النافذة في المنتصف
        payment_window.update_idletasks()
        x = (payment_window.winfo_screenwidth() // 2) - (200)
        y = (payment_window.winfo_screenheight() // 2) - (175)
        payment_window.geometry(f'400x350+{x}+{y}')

        result = [None]  # لحفظ النتيجة

        # إطار العنوان
        title_frame = tk.Frame(payment_window, bg=COLORS['success'], height=60)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text=f"طريقة الدفع - {supplier_name}",
                              font=(FONT_FAMILY, 14, 'bold'),
                              bg=COLORS['success'], fg='white')
        title_label.pack(expand=True)

        # معلومات المشترى
        info_frame = tk.Frame(payment_window, bg=COLORS['background'])
        info_frame.pack(fill=tk.X, padx=20, pady=10)

        tk.Label(info_frame, text=f"إجمالي المبلغ: {total_amount:.2f} {CURRENCY_SYMBOL}",
                font=(FONT_FAMILY, 12, 'bold'),
                bg=COLORS['background']).pack()

        # خيارات الدفع
        options_frame = tk.Frame(payment_window, bg=COLORS['background'])
        options_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        def cash_payment():
            """دفع نقدي كامل"""
            result[0] = ("نقدي", total_amount)
            payment_window.destroy()

        def installment_payment():
            """شراء بالتقسيط"""
            result[0] = ("بالتقسيط", 0.0)
            payment_window.destroy()

        def partial_payment():
            """دفع جزئي"""
            paid = simpledialog.askfloat(
                "دفع جزئي",
                f"إجمالي المبلغ: {total_amount:.2f} {CURRENCY_SYMBOL}\n\nأدخل المبلغ المدفوع:",
                minvalue=0.01,
                maxvalue=total_amount - 0.01,
                parent=payment_window
            )
            if paid is not None:
                result[0] = ("جزئي", paid)
                payment_window.destroy()

        # أزرار الخيارات
        tk.Button(options_frame, text="💰 دفع نقدي كامل",
                 command=cash_payment,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['success'], fg='white',
                 width=20, height=2).pack(pady=5)

        tk.Button(options_frame, text="📅 شراء بالتقسيط",
                 command=installment_payment,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['warning'], fg='white',
                 width=20, height=2).pack(pady=5)

        tk.Button(options_frame, text="💳 دفع جزئي",
                 command=partial_payment,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['info'], fg='white',
                 width=20, height=2).pack(pady=5)

        tk.Button(options_frame, text="❌ إلغاء",
                 command=payment_window.destroy,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['danger'], fg='white',
                 width=20).pack(pady=10)

        # انتظار اختيار المستخدم
        payment_window.wait_window()

        return result[0]

    def open_supplier_details(self, supplier_id, supplier_name):
        """فتح صفحة تفاصيل المورد"""
        try:
            # محاولة فتح نافذة تفاصيل المورد
            import importlib
            suppliers_module = importlib.import_module('gui.suppliers')
            if hasattr(suppliers_module, 'SupplierDetailsWindow'):
                suppliers_module.SupplierDetailsWindow(self.window.master, supplier_id, supplier_name, self.db)
            else:
                # كبديل، عرض رسالة بسيطة
                messagebox.showinfo("تم الشراء بالتقسيط", f"تم إضافة المبلغ لحساب المورد: {supplier_name}\nيمكنك مراجعة تفاصيل المورد من قائمة إدارة الموردين")
        except Exception as e:
            print(f"خطأ في فتح تفاصيل المورد: {e}")
            # كبديل، عرض رسالة بسيطة
            messagebox.showinfo("تم الشراء بالتقسيط", f"تم إضافة المبلغ لحساب المورد: {supplier_name}\nيمكنك مراجعة تفاصيل المورد من قائمة إدارة الموردين")

class PurchaseDetailsWindow:
    """نافذة عرض تفاصيل المشترى"""

    def __init__(self, parent, purchase_id, db):
        self.parent = parent
        self.purchase_id = purchase_id
        self.db = db

        self.window = tk.Toplevel(parent)
        self.window.title(f"تفاصيل المشترى رقم: {purchase_id}")
        self.window.geometry("900x600")
        self.window.configure(bg=COLORS['background'])
        self.window.transient(parent)

        self.create_interface()
        self.load_purchase_details()
        self.center_window()

    def center_window(self):
        """وضع النافذة في المنتصف"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def create_interface(self):
        """إنشاء واجهة تفاصيل المشترى"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg=COLORS['info'], height=60)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text=f"تفاصيل المشترى رقم: {self.purchase_id}",
                              font=(FONT_FAMILY, 16, 'bold'),
                              bg=COLORS['info'], fg='white')
        title_label.pack(expand=True)

        # إطار معلومات المشترى
        info_frame = tk.LabelFrame(self.window, text="معلومات المشترى",
                                  font=(FONT_FAMILY, 14, 'bold'),
                                  bg=COLORS['background'])
        info_frame.pack(fill=tk.X, padx=10, pady=5)

        # معلومات أساسية
        self.info_labels = {}
        info_data = [
            ("المورد:", "supplier_name"),
            ("التاريخ:", "purchase_date"),
            ("المبلغ الإجمالي:", "total_amount"),
            ("المدفوع:", "paid_amount"),
            ("المتبقي:", "remaining_amount"),
            ("الحالة:", "status")
        ]

        for i, (label_text, key) in enumerate(info_data):
            row = i // 2
            col = (i % 2) * 2

            tk.Label(info_frame, text=label_text,
                    font=(FONT_FAMILY, 12, 'bold'),
                    bg=COLORS['background']).grid(row=row, column=col, sticky=tk.W, padx=10, pady=5)

            self.info_labels[key] = tk.Label(info_frame, text="",
                                           font=(FONT_FAMILY, 12),
                                           bg=COLORS['background'])
            self.info_labels[key].grid(row=row, column=col+1, sticky=tk.W, padx=10, pady=5)

        # إطار جدول المنتجات
        table_frame = tk.LabelFrame(self.window, text="منتجات المشترى",
                                   font=(FONT_FAMILY, 14, 'bold'),
                                   bg=COLORS['background'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # جدول المنتجات
        columns = ('product_name', 'unit', 'quantity', 'unit_price', 'total_price')

        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)

        headers = {
            'product_name': 'المنتج',
            'unit': 'الوحدة',
            'quantity': 'الكمية',
            'unit_price': 'سعر الوحدة',
            'total_price': 'الإجمالي'
        }

        for col in columns:
            self.tree.heading(col, text=headers[col])
            if col == 'product_name':
                self.tree.column(col, width=250, anchor=tk.W)
            elif col == 'unit':
                self.tree.column(col, width=80, anchor=tk.CENTER)
            else:
                self.tree.column(col, width=120, anchor=tk.CENTER)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # إطار الملخص والأزرار
        bottom_frame = tk.Frame(self.window, bg=COLORS['background'])
        bottom_frame.pack(fill=tk.X, padx=10, pady=10)

        # الملخص
        self.summary_label = tk.Label(bottom_frame, text="",
                                     font=(FONT_FAMILY, 12, 'bold'),
                                     bg=COLORS['background'])
        self.summary_label.pack(side=tk.LEFT)

        # الأزرار
        buttons_frame = tk.Frame(bottom_frame, bg=COLORS['background'])
        buttons_frame.pack(side=tk.RIGHT)

        tk.Button(buttons_frame, text="🖨️ طباعة",
                 command=self.print_purchase,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['primary'], fg='white',
                 width=12).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="❌ إغلاق",
                 command=self.window.destroy,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['warning'], fg='white',
                 width=12).pack(side=tk.LEFT, padx=5)

    def load_purchase_details(self):
        """تحميل تفاصيل المشترى"""
        try:
            conn = self.db.db_manager.get_connection()
            cursor = conn.cursor()

            # تحميل معلومات المشترى
            cursor.execute('''
                SELECT p.*, s.name as supplier_name,
                       CASE
                           WHEN p.remaining_amount = 0 THEN 'مدفوع'
                           WHEN p.paid_amount = 0 THEN 'غير مدفوع'
                           ELSE 'مدفوع جزئياً'
                       END as status
                FROM purchases p
                LEFT JOIN suppliers s ON p.supplier_id = s.id
                WHERE p.id = ?
            ''', (self.purchase_id,))

            purchase = cursor.fetchone()

            if purchase:
                # تحديث معلومات المشترى
                self.info_labels['supplier_name'].config(text=purchase[7])
                self.info_labels['purchase_date'].config(text=purchase[2])
                self.info_labels['total_amount'].config(text=f"{purchase[3]:.2f} {CURRENCY_SYMBOL}")
                self.info_labels['paid_amount'].config(text=f"{purchase[4]:.2f} {CURRENCY_SYMBOL}")
                self.info_labels['remaining_amount'].config(text=f"{purchase[5]:.2f} {CURRENCY_SYMBOL}")
                self.info_labels['status'].config(text=purchase[8])

                # تلوين حالة الدفع
                if purchase[5] > 0:  # remaining_amount
                    if purchase[4] == 0:  # paid_amount
                        self.info_labels['status'].config(fg=COLORS['danger'])
                    else:
                        self.info_labels['status'].config(fg=COLORS['warning'])
                else:
                    self.info_labels['status'].config(fg=COLORS['success'])

            # تحميل منتجات المشترى
            cursor.execute('''
                SELECT pi.*, p.name as product_name, p.unit
                FROM purchase_items pi
                LEFT JOIN products p ON pi.product_id = p.id
                WHERE pi.purchase_id = ?
            ''', (self.purchase_id,))

            items = cursor.fetchall()
            conn.close()

            total_items = 0
            total_quantity = 0

            for item in items:
                total_price = item[3] * item[4]  # quantity * unit_price

                self.tree.insert('', tk.END, values=(
                    item[5],  # product_name
                    item[6],  # unit
                    item[3],  # quantity
                    f"{item[4]:.2f}",  # unit_price
                    f"{total_price:.2f}"  # total_price
                ))

                total_items += 1
                total_quantity += item[3]

            # تحديث الملخص
            self.summary_label.config(
                text=f"إجمالي المنتجات: {total_items} | إجمالي الكمية: {total_quantity}"
            )

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل تفاصيل المشترى: {str(e)}")

    def print_purchase(self):
        """طباعة المشترى"""
        try:
            # يمكن إضافة وظيفة الطباعة هنا
            messagebox.showinfo("معلومة", "وظيفة الطباعة ستتوفر قريباً")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة المشترى: {str(e)}")

if __name__ == "__main__":
    # اختبار النافذة
    app = PurchasesWindow()
    app.window.mainloop()
