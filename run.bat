@echo off
echo ========================================
echo    برنامج محاسبة الصيدلية الزراعية
echo ========================================
echo.
echo جاري تشغيل البرنامج...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.7 أو أحدث من https://python.org
    pause
    exit /b 1
)

REM تشغيل البرنامج
python main.py

REM في حالة حدوث خطأ
if errorlevel 1 (
    echo.
    echo حدث خطأ أثناء تشغيل البرنامج
    echo يرجى التأكد من تثبيت جميع المتطلبات بتشغيل: pip install -r requirements.txt
    pause
)
