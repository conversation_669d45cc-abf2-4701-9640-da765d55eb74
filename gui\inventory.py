# -*- coding: utf-8 -*-
"""
واجهة إدارة المخزون والمنتجات لبرنامج محاسبة الصيدلية الزراعية
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import *
from database.database import PharmacyDatabase

class InventoryWindow:
    """نافذة إدارة المخزون"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.db = PharmacyDatabase()
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.setup_window()
        self.create_interface()
        self.load_products()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة المخزون والمنتجات")
        self.window.geometry("1000x700")
        self.window.configure(bg=COLORS['background'])
        
        # جعل النافذة في المنتصف
        self.center_window()
        
    def center_window(self):
        """وضع النافذة في منتصف الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg=COLORS['primary'], height=60)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="إدارة المخزون والمنتجات",
                              font=(FONT_FAMILY, 18, 'bold'),
                              bg=COLORS['primary'], fg='white')
        title_label.pack(expand=True)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # أزرار العمليات
        tk.Button(buttons_frame, text="إضافة منتج جديد",
                 command=self.add_product,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['success'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تعديل منتج",
                 command=self.edit_product,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['primary'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="حذف منتج",
                 command=self.delete_product,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['warning'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تحديث المخزون",
                 command=self.update_stock,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['secondary'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        # إطار البحث
        search_frame = tk.Frame(self.window, bg=COLORS['background'])
        search_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(search_frame, text="البحث:",
                font=(FONT_FAMILY, 12),
                bg=COLORS['background']).pack(side=tk.LEFT, padx=5)
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.filter_products)
        search_entry = tk.Entry(search_frame, textvariable=self.search_var,
                               font=(FONT_FAMILY, 12), width=30)
        search_entry.pack(side=tk.LEFT, padx=5)
        
        # إطار الجدول
        table_frame = tk.Frame(self.window, bg=COLORS['background'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # إنشاء الجدول
        self.create_products_table(table_frame)
        
        # إطار المعلومات
        info_frame = tk.Frame(self.window, bg=COLORS['background'])
        info_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.info_label = tk.Label(info_frame, text="",
                                  font=(FONT_FAMILY, 10),
                                  bg=COLORS['background'])
        self.info_label.pack(anchor=tk.W)
        
    def create_products_table(self, parent):
        """إنشاء جدول المنتجات"""
        # إنشاء Treeview
        columns = ('id', 'name', 'category', 'unit', 'purchase_price', 
                  'selling_price', 'stock_quantity', 'min_stock_level', 'supplier_name')
        
        self.tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة
        headers = {
            'id': 'الرقم',
            'name': 'اسم المنتج',
            'category': 'الفئة',
            'unit': 'الوحدة',
            'purchase_price': 'سعر الشراء',
            'selling_price': 'سعر البيع',
            'stock_quantity': 'الكمية',
            'min_stock_level': 'الحد الأدنى',
            'supplier_name': 'المورد'
        }
        
        for col in columns:
            self.tree.heading(col, text=headers[col])
            self.tree.column(col, width=100, anchor=tk.CENTER)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.tree.bind('<Double-1>', self.on_item_double_click)
        
    def load_products(self):
        """تحميل المنتجات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل المنتجات من قاعدة البيانات
            products = self.db.get_all_products()
            
            for product in products:
                # تلوين الصفوف حسب مستوى المخزون
                tags = []
                if product['stock_quantity'] <= product['min_stock_level']:
                    tags.append('low_stock')
                elif product['stock_quantity'] == 0:
                    tags.append('out_of_stock')
                
                self.tree.insert('', tk.END, values=(
                    product['id'],
                    product['name'],
                    product['category'],
                    product['unit'],
                    f"{product['purchase_price']:.2f}",
                    f"{product['selling_price']:.2f}",
                    product['stock_quantity'],
                    product['min_stock_level'],
                    product['supplier_name'] or 'غير محدد'
                ), tags=tags)
            
            # تعيين ألوان للصفوف
            self.tree.tag_configure('low_stock', background='#ffcccc')
            self.tree.tag_configure('out_of_stock', background='#ff9999')
            
            # تحديث معلومات الإحصائيات
            self.update_info_label(len(products))
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المنتجات: {str(e)}")
    
    def update_info_label(self, total_products):
        """تحديث تسمية المعلومات"""
        try:
            low_stock_products = self.db.get_low_stock_products()
            low_stock_count = len(low_stock_products)
            
            info_text = f"إجمالي المنتجات: {total_products} | منتجات منخفضة المخزون: {low_stock_count}"
            self.info_label.config(text=info_text)
            
        except Exception:
            self.info_label.config(text=f"إجمالي المنتجات: {total_products}")
    
    def filter_products(self, *args):
        """تصفية المنتجات حسب النص المدخل"""
        search_text = self.search_var.get().lower()
        
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        try:
            # تحميل جميع المنتجات
            products = self.db.get_all_products()
            
            # تصفية المنتجات
            filtered_products = []
            for product in products:
                if (search_text in product['name'].lower() or 
                    search_text in product['category'].lower() or
                    search_text in (product['supplier_name'] or '').lower()):
                    filtered_products.append(product)
            
            # عرض المنتجات المصفاة
            for product in filtered_products:
                tags = []
                if product['stock_quantity'] <= product['min_stock_level']:
                    tags.append('low_stock')
                elif product['stock_quantity'] == 0:
                    tags.append('out_of_stock')
                
                self.tree.insert('', tk.END, values=(
                    product['id'],
                    product['name'],
                    product['category'],
                    product['unit'],
                    f"{product['purchase_price']:.2f}",
                    f"{product['selling_price']:.2f}",
                    product['stock_quantity'],
                    product['min_stock_level'],
                    product['supplier_name'] or 'غير محدد'
                ), tags=tags)
            
            # تحديث المعلومات
            self.update_info_label(len(filtered_products))
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصفية المنتجات: {str(e)}")

    def add_product(self):
        """إضافة منتج جديد"""
        dialog = ProductDialog(self.window, "إضافة منتج جديد")
        if dialog.result:
            try:
                product_data = dialog.result
                self.db.add_product(
                    name=product_data['name'],
                    category=product_data['category'],
                    unit=product_data['unit'],
                    purchase_price=product_data['purchase_price'],
                    selling_price=product_data['selling_price'],
                    stock_quantity=product_data['stock_quantity'],
                    min_stock_level=product_data['min_stock_level']
                )
                messagebox.showinfo("نجح", "تم إضافة المنتج بنجاح")
                self.load_products()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة المنتج: {str(e)}")

    def edit_product(self):
        """تعديل منتج محدد"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للتعديل")
            return

        # الحصول على بيانات المنتج المحدد
        item_values = self.tree.item(selected_item[0])['values']
        product_id = item_values[0]

        # فتح نافذة التعديل
        messagebox.showinfo("قريباً", "سيتم تطوير تعديل المنتجات قريباً")

    def delete_product(self):
        """حذف منتج محدد"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للحذف")
            return

        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا المنتج؟"):
            messagebox.showinfo("قريباً", "سيتم تطوير حذف المنتجات قريباً")

    def update_stock(self):
        """تحديث مخزون منتج"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج لتحديث مخزونه")
            return

        # الحصول على بيانات المنتج
        item_values = self.tree.item(selected_item[0])['values']
        product_id = item_values[0]
        product_name = item_values[1]
        current_stock = item_values[6]

        # طلب الكمية الجديدة
        new_quantity = simpledialog.askinteger(
            "تحديث المخزون",
            f"المنتج: {product_name}\nالكمية الحالية: {current_stock}\n\nأدخل الكمية الجديدة:",
            minvalue=0
        )

        if new_quantity is not None:
            try:
                # حساب التغيير في الكمية
                quantity_change = new_quantity - int(current_stock)
                self.db.update_product_stock(product_id, quantity_change)
                messagebox.showinfo("نجح", "تم تحديث المخزون بنجاح")
                self.load_products()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحديث المخزون: {str(e)}")

    def on_item_double_click(self, event):
        """التعامل مع النقر المزدوج على عنصر"""
        self.edit_product()

class ProductDialog:
    """نافذة حوار إضافة/تعديل منتج"""

    def __init__(self, parent, title="إضافة منتج"):
        self.result = None
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x500")
        self.dialog.configure(bg=COLORS['background'])
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.create_form()
        self.center_dialog()

    def center_dialog(self):
        """وضع النافذة في المنتصف"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f'{width}x{height}+{x}+{y}')

    def create_form(self):
        """إنشاء نموذج إدخال البيانات"""
        # إطار النموذج
        form_frame = tk.Frame(self.dialog, bg=COLORS['background'])
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # حقول الإدخال
        self.entries = {}

        fields = [
            ('name', 'اسم المنتج', 'str'),
            ('category', 'الفئة', 'combo'),
            ('unit', 'الوحدة', 'combo'),
            ('purchase_price', 'سعر الشراء', 'float'),
            ('selling_price', 'سعر البيع', 'float'),
            ('stock_quantity', 'الكمية الحالية', 'int'),
            ('min_stock_level', 'الحد الأدنى للمخزون', 'int')
        ]

        for i, (field, label, field_type) in enumerate(fields):
            # تسمية الحقل
            tk.Label(form_frame, text=label + ":",
                    font=(FONT_FAMILY, 12),
                    bg=COLORS['background']).grid(row=i, column=0, sticky=tk.W, pady=5)

            # حقل الإدخال
            if field_type == 'combo':
                if field == 'category':
                    values = DEFAULT_CATEGORIES
                elif field == 'unit':
                    values = DEFAULT_UNITS

                entry = ttk.Combobox(form_frame, values=values,
                                   font=(FONT_FAMILY, 12), width=25)
            else:
                entry = tk.Entry(form_frame, font=(FONT_FAMILY, 12), width=28)

            entry.grid(row=i, column=1, sticky=tk.W, pady=5, padx=10)
            self.entries[field] = entry

        # أزرار العمليات
        buttons_frame = tk.Frame(form_frame, bg=COLORS['background'])
        buttons_frame.grid(row=len(fields), column=0, columnspan=2, pady=20)

        tk.Button(buttons_frame, text="حفظ",
                 command=self.save_product,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['success'], fg='white',
                 width=10).pack(side=tk.LEFT, padx=10)

        tk.Button(buttons_frame, text="إلغاء",
                 command=self.dialog.destroy,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['warning'], fg='white',
                 width=10).pack(side=tk.LEFT, padx=10)

    def save_product(self):
        """حفظ بيانات المنتج"""
        try:
            # التحقق من صحة البيانات
            data = {}

            # اسم المنتج
            name = self.entries['name'].get().strip()
            if not name:
                raise ValueError("يرجى إدخال اسم المنتج")
            data['name'] = name

            # الفئة
            category = self.entries['category'].get().strip()
            if not category:
                raise ValueError("يرجى اختيار فئة المنتج")
            data['category'] = category

            # الوحدة
            unit = self.entries['unit'].get().strip()
            if not unit:
                raise ValueError("يرجى اختيار وحدة المنتج")
            data['unit'] = unit

            # الأسعار
            try:
                purchase_price = float(self.entries['purchase_price'].get())
                if purchase_price < 0:
                    raise ValueError("سعر الشراء يجب أن يكون أكبر من أو يساوي صفر")
                data['purchase_price'] = purchase_price
            except ValueError:
                raise ValueError("يرجى إدخال سعر شراء صحيح")

            try:
                selling_price = float(self.entries['selling_price'].get())
                if selling_price < 0:
                    raise ValueError("سعر البيع يجب أن يكون أكبر من أو يساوي صفر")
                data['selling_price'] = selling_price
            except ValueError:
                raise ValueError("يرجى إدخال سعر بيع صحيح")

            # الكميات
            try:
                stock_quantity = int(self.entries['stock_quantity'].get())
                if stock_quantity < 0:
                    raise ValueError("الكمية يجب أن تكون أكبر من أو تساوي صفر")
                data['stock_quantity'] = stock_quantity
            except ValueError:
                raise ValueError("يرجى إدخال كمية صحيحة")

            try:
                min_stock_level = int(self.entries['min_stock_level'].get())
                if min_stock_level < 0:
                    raise ValueError("الحد الأدنى يجب أن يكون أكبر من أو يساوي صفر")
                data['min_stock_level'] = min_stock_level
            except ValueError:
                raise ValueError("يرجى إدخال حد أدنى صحيح")

            # حفظ البيانات
            self.result = data
            self.dialog.destroy()

        except ValueError as e:
            messagebox.showerror("خطأ في البيانات", str(e))

if __name__ == "__main__":
    # اختبار النافذة
    app = InventoryWindow()
    app.window.mainloop()
