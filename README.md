# برنامج محاسبة الصيدلية الزراعية

برنامج محاسبة شامل مصمم خصيصاً للصيدليات الزراعية، يوفر إدارة متكاملة للمبيعات والمخزون والزبائن والموردين.

## المميزات الرئيسية

### ✅ المميزات المكتملة

#### 🏪 إدارة شاملة للصيدلية
- **إدارة المنتجات**: إضافة وتعديل وحذف المنتجات مع تتبع المخزون الذكي
- **إدارة الزبائن**: ملفات شخصية كاملة مع تتبع الديون والحدود الائتمانية
- **إدارة الموردين**: معلومات الموردين وربطها بالمنتجات
- **إدارة المبيعات**: نظام بيع متكامل مع دعم البيع النقدي والآجل

#### 💰 النظام المالي
- **تسجيل المبيعات**: واجهة سهلة لتسجيل المبيعات مع حساب الإجماليات
- **إدارة المدفوعات**: تتبع المدفوعات وتحديث الديون تلقائياً
- **طرق دفع متعددة**: نقدي، شيك، تحويل بنكي، بطاقة ائتمان، آجل
- **تقارير مالية شاملة**: تقارير يومية وشهرية وتحليل الأرباح

#### 📊 التقارير والإحصائيات
- **تقارير المبيعات**: يومية وشهرية مع تحليل مفصل
- **تقارير الأرباح**: حساب الأرباح وهوامش الربح
- **تقارير الديون**: قوائم الزبائن المدينين والمبالغ المستحقة
- **تقارير المخزون**: حالة المخزون والمنتجات منخفضة المخزون

#### 🔒 الأمان والنسخ الاحتياطية
- **نسخ احتياطية تلقائية**: كل 7 أيام مع إمكانية النسخ اليدوي
- **استعادة البيانات**: استعادة آمنة من النسخ الاحتياطية
- **حماية البيانات**: منع حذف البيانات المرتبطة وتأكيدات الأمان

#### 🎨 واجهة محسنة
- **تصميم عصري**: ألوان متناسقة وتخطيط احترافي
- **معلومات سريعة**: لوحة معلومات تفاعلية في الواجهة الرئيسية
- **تنبيهات بصرية**: ألوان مختلفة للحالات المختلفة
- **سهولة الاستخدام**: واجهات بديهية ورسائل واضحة

## متطلبات التشغيل

- Python 3.7 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux
- المكتبات المطلوبة (موجودة في requirements.txt)

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd محاسب
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل البرنامج

#### 🚀 الطريقة المُوصى بها: التشغيل المحسن
```bash
# في Windows - التشغيل الآمن والمحسن مع فحص شامل
run_enhanced.bat

# أو التشغيل العادي
run.bat
```

#### 🔧 الطريقة البديلة: التشغيل المباشر
```bash
# التشغيل الآمن مع فحص المتطلبات
python run_safe.py

# التشغيل العادي
python main.py
```

#### ⚡ التشغيل السريع (للمطورين)
```bash
python main.py
```

## هيكل المشروع

```
محاسب/
├── main.py                 # الملف الرئيسي
├── requirements.txt        # المكتبات المطلوبة
├── README.md              # ملف التوثيق
├── database/              # وحدة قاعدة البيانات
│   ├── __init__.py
│   ├── models.py          # نماذج قاعدة البيانات
│   └── database.py        # عمليات قاعدة البيانات
├── gui/                   # واجهات المستخدم
│   ├── __init__.py
│   ├── main_window.py     # النافذة الرئيسية
│   └── inventory.py       # واجهة إدارة المخزون
├── utils/                 # الأدوات المساعدة
│   ├── __init__.py
│   └── backup.py          # النسخ الاحتياطية
└── config/                # الإعدادات
    ├── __init__.py
    └── settings.py        # إعدادات البرنامج
```

## الاستخدام

### البدء السريع
1. **التثبيت**: شغل `install.bat` لتثبيت المتطلبات
2. **البيانات التجريبية**: شغل `add_sample_data.bat` لإضافة بيانات تجريبية
3. **التشغيل**: شغل `run.bat` لبدء البرنامج

### إدارة المبيعات
1. **بيع جديد**: اضغط "بيع جديد" من الواجهة الرئيسية
2. **اختيار الزبون**: اختر زبون من القائمة أو "زبون نقدي"
3. **إضافة المنتجات**: اختر المنتج، أدخل الكمية والسعر، اضغط "إضافة"
4. **إتمام البيع**: اضغط "حفظ البيع" وأدخل المبلغ المدفوع
5. **عرض المبيعات**: اضغط "تقارير المبيعات" لعرض جميع المبيعات

### إدارة الزبائن
1. **إضافة زبون**: اضغط "إدارة الزبائن" ثم "إضافة زبون جديد"
2. **تعديل البيانات**: اختر زبون واضغط "تعديل زبون"
3. **إدارة الديون**: اضغط "إضافة دفعة" لتسجيل دفعة جديدة
4. **عرض المدفوعات**: اضغط "عرض المدفوعات" لرؤية سجل المدفوعات

### إدارة المنتجات
1. **إضافة منتج**: اضغط "إدارة المنتجات" ثم "إضافة منتج جديد"
2. **تحديث المخزون**: اختر منتج واضغط "تحديث المخزون"
3. **البحث**: استخدم مربع البحث للعثور على منتج معين
4. **تنبيهات المخزون**: المنتجات منخفضة المخزون تظهر بألوان تحذيرية

### إدارة الموردين
1. **إضافة مورد**: اضغط "إدارة الموردين" ثم "إضافة مورد جديد"
2. **ربط المنتجات**: عند إضافة منتج، اختر المورد المناسب
3. **عرض منتجات المورد**: اختر مورد واضغط "عرض المنتجات"

### التقارير
- **التقرير اليومي**: من قائمة "التقارير" → "تقرير المبيعات اليومية"
- **التقرير الشهري**: من قائمة "التقارير" → "تقرير المبيعات الشهرية"
- **تقرير الأرباح**: من قائمة "التقارير" → "تقرير الأرباح"
- **تقرير الديون**: اضغط "تقرير الديون" من الواجهة الرئيسية

### النسخ الاحتياطية
- **تلقائية**: تتم كل 7 أيام تلقائياً
- **يدوية**: من قائمة "ملف" → "نسخة احتياطية"
- **الاستعادة**: من قائمة "ملف" → "استعادة نسخة احتياطية"

## الإعدادات

يمكن تخصيص البرنامج من خلال ملف `config/settings.py`:

- **اسم الشركة**: تغيير اسم الصيدلية
- **العملة**: تغيير رمز العملة
- **فئات المنتجات**: إضافة أو تعديل فئات المنتجات
- **وحدات القياس**: تخصيص وحدات القياس
- **ألوان الواجهة**: تغيير ألوان البرنامج

## قاعدة البيانات

البرنامج يستخدم قاعدة بيانات SQLite محلية تحتوي على الجداول التالية:

- **products**: المنتجات والمخزون
- **customers**: بيانات الزبائن
- **suppliers**: بيانات الموردين
- **sales**: المبيعات
- **sale_items**: تفاصيل المبيعات
- **purchases**: المشتريات
- **purchase_items**: تفاصيل المشتريات
- **payments**: المدفوعات

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. تحقق من ملف التوثيق هذا
2. راجع رسائل الخطأ في البرنامج
3. تأكد من تثبيت جميع المتطلبات بشكل صحيح

## خطة التطوير المستقبلية

### المرحلة التالية
- [ ] إكمال وحدة المبيعات
- [ ] إضافة إدارة الزبائن
- [ ] تطوير نظام التقارير
- [ ] إضافة طباعة الفواتير

### مميزات متقدمة
- [ ] واجهة ويب
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة الدفع
- [ ] تقارير متقدمة بالرسوم البيانية

## الترخيص

هذا البرنامج مطور لأغراض تعليمية وتجارية. جميع الحقوق محفوظة.

---

**تم التطوير بواسطة**: فريق التطوير  
**الإصدار**: 1.0  
**تاريخ آخر تحديث**: ديسمبر 2024
