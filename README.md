# برنامج محاسبة الصيدلية الزراعية

برنامج محاسبة شامل مصمم خصيصاً للصيدليات الزراعية، يوفر إدارة متكاملة للمبيعات والمخزون والزبائن والموردين.

## المميزات الرئيسية

### ✅ المميزات المكتملة
- **الواجهة الرئيسية**: واجهة سهلة الاستخدام باللغة العربية
- **إدارة المنتجات**: إضافة وتعديل وحذف المنتجات مع تتبع المخزون
- **قاعدة البيانات**: نظام قاعدة بيانات SQLite محلي وآمن
- **النسخ الاحتياطية**: نظام نسخ احتياطي تلقائي ويدوي
- **تنبيهات المخزون**: تنبيهات للمنتجات منخفضة المخزون

### 🚧 المميزات قيد التطوير
- **إدارة المبيعات**: تسجيل المبيعات اليومية والفواتير
- **إدارة الزبائن**: ملفات شخصية للزبائن وتتبع الديون
- **إدارة الموردين**: معلومات الموردين والمشتريات
- **التقارير المالية**: تقارير شاملة للمبيعات والأرباح
- **إدارة الديون**: تتبع المدفوعات والديون المستحقة

## متطلبات التشغيل

- Python 3.7 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux
- المكتبات المطلوبة (موجودة في requirements.txt)

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd محاسب
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل البرنامج
```bash
python main.py
```

## هيكل المشروع

```
محاسب/
├── main.py                 # الملف الرئيسي
├── requirements.txt        # المكتبات المطلوبة
├── README.md              # ملف التوثيق
├── database/              # وحدة قاعدة البيانات
│   ├── __init__.py
│   ├── models.py          # نماذج قاعدة البيانات
│   └── database.py        # عمليات قاعدة البيانات
├── gui/                   # واجهات المستخدم
│   ├── __init__.py
│   ├── main_window.py     # النافذة الرئيسية
│   └── inventory.py       # واجهة إدارة المخزون
├── utils/                 # الأدوات المساعدة
│   ├── __init__.py
│   └── backup.py          # النسخ الاحتياطية
└── config/                # الإعدادات
    ├── __init__.py
    └── settings.py        # إعدادات البرنامج
```

## الاستخدام

### إدارة المنتجات
1. من القائمة الرئيسية، اختر "إدارة المنتجات"
2. لإضافة منتج جديد، اضغط "إضافة منتج جديد"
3. املأ البيانات المطلوبة واضغط "حفظ"
4. يمكنك البحث في المنتجات باستخدام مربع البحث
5. لتحديث المخزون، اختر المنتج واضغط "تحديث المخزون"

### النسخ الاحتياطية
- يتم إنشاء نسخ احتياطية تلقائياً كل 7 أيام
- يمكن إنشاء نسخة احتياطية يدوياً من قائمة "ملف"
- النسخ الاحتياطية محفوظة في مجلد "backups"

## الإعدادات

يمكن تخصيص البرنامج من خلال ملف `config/settings.py`:

- **اسم الشركة**: تغيير اسم الصيدلية
- **العملة**: تغيير رمز العملة
- **فئات المنتجات**: إضافة أو تعديل فئات المنتجات
- **وحدات القياس**: تخصيص وحدات القياس
- **ألوان الواجهة**: تغيير ألوان البرنامج

## قاعدة البيانات

البرنامج يستخدم قاعدة بيانات SQLite محلية تحتوي على الجداول التالية:

- **products**: المنتجات والمخزون
- **customers**: بيانات الزبائن
- **suppliers**: بيانات الموردين
- **sales**: المبيعات
- **sale_items**: تفاصيل المبيعات
- **purchases**: المشتريات
- **purchase_items**: تفاصيل المشتريات
- **payments**: المدفوعات

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. تحقق من ملف التوثيق هذا
2. راجع رسائل الخطأ في البرنامج
3. تأكد من تثبيت جميع المتطلبات بشكل صحيح

## خطة التطوير المستقبلية

### المرحلة التالية
- [ ] إكمال وحدة المبيعات
- [ ] إضافة إدارة الزبائن
- [ ] تطوير نظام التقارير
- [ ] إضافة طباعة الفواتير

### مميزات متقدمة
- [ ] واجهة ويب
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة الدفع
- [ ] تقارير متقدمة بالرسوم البيانية

## الترخيص

هذا البرنامج مطور لأغراض تعليمية وتجارية. جميع الحقوق محفوظة.

---

**تم التطوير بواسطة**: فريق التطوير  
**الإصدار**: 1.0  
**تاريخ آخر تحديث**: ديسمبر 2024
