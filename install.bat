@echo off
echo ========================================
echo    تثبيت متطلبات برنامج محاسبة الصيدلية
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.7 أو أحدث من https://python.org
    pause
    exit /b 1
)

echo جاري تثبيت المتطلبات...
echo.

REM تثبيت المتطلبات
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo حدث خطأ أثناء تثبيت المتطلبات
    echo يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى
    pause
    exit /b 1
) else (
    echo.
    echo تم تثبيت جميع المتطلبات بنجاح!
    echo يمكنك الآن تشغيل البرنامج بالنقر على run.bat
    echo.
)

pause
