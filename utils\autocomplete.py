# -*- coding: utf-8 -*-
"""
نظام التلميحات التلقائية (AutoComplete) لبرنامج محاسبة الصيدلية الزراعية
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import *

class AutoCompleteEntry(tk.Entry):
    """حقل إدخال مع تلميحات تلقائية"""
    
    def __init__(self, parent, autocomplete_list=None, listbox_length=6, ignore_case=True, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.autocomplete_list = autocomplete_list or []
        self.listbox_length = listbox_length
        self.ignore_case = ignore_case
        self.var = self["textvariable"]
        
        if self.var == '':
            self.var = self["textvariable"] = tk.StringVar()
        
        self.var.trace('w', self.changed)
        self.bind("<Right>", self.selection)
        self.bind("<Up>", self.up)
        self.bind("<Down>", self.down)
        self.bind("<Return>", self.selection)
        self.bind("<Tab>", self.selection)
        self.bind("<Escape>", self.hide_listbox)
        
        self.lb_up = False
        self.lb = None
        
    def changed(self, name, index, mode):
        """عند تغيير النص في الحقل"""
        if self.var.get() == '':
            if self.lb_up:
                self.hide_listbox()
        else:
            words = self.comparison()
            if words:
                if not self.lb_up:
                    self.show_listbox(words)
                else:
                    self.update_listbox(words)
            else:
                if self.lb_up:
                    self.hide_listbox()
    
    def selection(self, event):
        """عند اختيار عنصر من القائمة"""
        if self.lb_up:
            self.var.set(self.lb.get(tk.ACTIVE))
            self.hide_listbox()
            self.icursor(tk.END)
        return 'break'
    
    def up(self, event):
        """التحرك لأعلى في القائمة"""
        if self.lb_up:
            if self.lb.curselection() == ():
                index = '0'
            else:
                index = self.lb.curselection()[0]
            if index != '0':
                self.lb.selection_clear(first=index)
                index = str(int(index) - 1)
                self.lb.selection_set(first=index)
                self.lb.activate(index)
        return 'break'
    
    def down(self, event):
        """التحرك لأسفل في القائمة"""
        if self.lb_up:
            if self.lb.curselection() == ():
                index = '0'
            else:
                index = self.lb.curselection()[0]
            if index != str(self.lb.size() - 1):
                self.lb.selection_clear(first=index)
                index = str(int(index) + 1)
                self.lb.selection_set(first=index)
                self.lb.activate(index)
        return 'break'
    
    def comparison(self):
        """مقارنة النص المدخل مع القائمة"""
        pattern = self.var.get()
        if self.ignore_case:
            pattern = pattern.lower()
        
        matches = []
        for item in self.autocomplete_list:
            if self.ignore_case:
                if item.lower().startswith(pattern):
                    matches.append(item)
            else:
                if item.startswith(pattern):
                    matches.append(item)
        
        return matches
    
    def show_listbox(self, words):
        """عرض قائمة التلميحات"""
        self.lb = tk.Listbox(self.master, height=min(self.listbox_length, len(words)))
        self.lb.bind("<Double-Button-1>", self.selection)
        self.lb.bind("<Return>", self.selection)
        
        # تحديد موقع القائمة
        x = self.winfo_x()
        y = self.winfo_y() + self.winfo_height()
        self.lb.place(x=x, y=y, width=self.winfo_width())
        
        # إضافة الكلمات للقائمة
        for word in words:
            self.lb.insert(tk.END, word)
        
        self.lb.selection_set(first=0)
        self.lb.activate(0)
        self.lb_up = True
    
    def update_listbox(self, words):
        """تحديث قائمة التلميحات"""
        if self.lb:
            self.lb.delete(0, tk.END)
            for word in words:
                self.lb.insert(tk.END, word)
            
            if words:
                self.lb.selection_set(first=0)
                self.lb.activate(0)
    
    def hide_listbox(self, event=None):
        """إخفاء قائمة التلميحات"""
        if self.lb:
            self.lb.destroy()
            self.lb = None
        self.lb_up = False
        return 'break'
    
    def set_completion_list(self, completion_list):
        """تحديث قائمة التلميحات"""
        self.autocomplete_list = completion_list

class AutoCompleteCombobox(ttk.Combobox):
    """قائمة منسدلة مع تلميحات تلقائية"""
    
    def __init__(self, parent, autocomplete_list=None, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.autocomplete_list = autocomplete_list or []
        self['values'] = self.autocomplete_list
        
        self.bind('<KeyRelease>', self.on_keyrelease)
        self.bind('<Button-1>', self.on_click)
        
    def on_keyrelease(self, event):
        """عند تحرير مفتاح"""
        value = event.widget.get()
        
        if value == '':
            self['values'] = self.autocomplete_list
        else:
            data = []
            for item in self.autocomplete_list:
                if value.lower() in item.lower():
                    data.append(item)
            
            self['values'] = data
            
        # فتح القائمة المنسدلة
        if data:
            self.event_generate('<Button-1>')
    
    def on_click(self, event):
        """عند النقر على القائمة"""
        self.event_generate('<Down>')
    
    def set_completion_list(self, completion_list):
        """تحديث قائمة التلميحات"""
        self.autocomplete_list = completion_list
        self['values'] = completion_list

class SmartSearchEntry(tk.Frame):
    """حقل بحث ذكي مع تلميحات وفلترة"""
    
    def __init__(self, parent, search_callback=None, placeholder="البحث...", **kwargs):
        super().__init__(parent, **kwargs)
        
        self.search_callback = search_callback
        self.placeholder = placeholder
        self.create_widgets()
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # حقل البحث
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        
        self.search_entry = tk.Entry(self, textvariable=self.search_var,
                                    font=(FONT_FAMILY, 12),
                                    relief=tk.SOLID, bd=1)
        self.search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # زر مسح البحث
        self.clear_button = tk.Button(self, text="✕", 
                                     command=self.clear_search,
                                     font=(FONT_FAMILY, 10),
                                     width=3, relief=tk.FLAT,
                                     bg=COLORS['background'])
        self.clear_button.pack(side=tk.RIGHT, padx=2)
        
        # إضافة placeholder
        self.add_placeholder()
        
    def add_placeholder(self):
        """إضافة نص توضيحي"""
        self.search_entry.insert(0, self.placeholder)
        self.search_entry.config(fg='gray')
        
        self.search_entry.bind('<FocusIn>', self.on_focus_in)
        self.search_entry.bind('<FocusOut>', self.on_focus_out)
    
    def on_focus_in(self, event):
        """عند التركيز على الحقل"""
        if self.search_entry.get() == self.placeholder:
            self.search_entry.delete(0, tk.END)
            self.search_entry.config(fg='black')
    
    def on_focus_out(self, event):
        """عند فقدان التركيز"""
        if self.search_entry.get() == '':
            self.search_entry.insert(0, self.placeholder)
            self.search_entry.config(fg='gray')
    
    def on_search_change(self, *args):
        """عند تغيير نص البحث"""
        search_text = self.search_var.get()
        if search_text != self.placeholder and self.search_callback:
            self.search_callback(search_text)
    
    def clear_search(self):
        """مسح نص البحث"""
        self.search_entry.delete(0, tk.END)
        self.search_entry.focus()
        if self.search_callback:
            self.search_callback('')
    
    def get(self):
        """الحصول على نص البحث"""
        text = self.search_var.get()
        return text if text != self.placeholder else ''
    
    def set(self, value):
        """تعيين نص البحث"""
        self.search_entry.delete(0, tk.END)
        self.search_entry.insert(0, value)
        self.search_entry.config(fg='black')

class DataValidator:
    """فئة للتحقق من صحة البيانات"""
    
    @staticmethod
    def validate_phone(phone):
        """التحقق من صحة رقم الهاتف"""
        if not phone:
            return True  # اختياري
        
        # إزالة المسافات والرموز
        clean_phone = ''.join(filter(str.isdigit, phone))
        
        # التحقق من الطول (10 أرقام للسعودية)
        if len(clean_phone) == 10 and clean_phone.startswith('05'):
            return True
        elif len(clean_phone) == 13 and clean_phone.startswith('966'):
            return True
        
        return False
    
    @staticmethod
    def validate_email(email):
        """التحقق من صحة البريد الإلكتروني"""
        if not email:
            return True  # اختياري
        
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def validate_number(value, min_value=None, max_value=None):
        """التحقق من صحة الرقم"""
        try:
            num = float(value)
            if min_value is not None and num < min_value:
                return False
            if max_value is not None and num > max_value:
                return False
            return True
        except ValueError:
            return False
    
    @staticmethod
    def format_phone(phone):
        """تنسيق رقم الهاتف"""
        if not phone:
            return phone
        
        clean_phone = ''.join(filter(str.isdigit, phone))
        
        if len(clean_phone) == 10 and clean_phone.startswith('05'):
            return f"{clean_phone[:3]} {clean_phone[3:6]} {clean_phone[6:]}"
        elif len(clean_phone) == 13 and clean_phone.startswith('966'):
            return f"+966 {clean_phone[3:5]} {clean_phone[5:8]} {clean_phone[8:]}"
        
        return phone
    
    @staticmethod
    def format_currency(amount):
        """تنسيق المبلغ"""
        try:
            return f"{float(amount):,.2f}"
        except:
            return str(amount)

if __name__ == "__main__":
    # اختبار النظام
    root = tk.Tk()
    root.title("اختبار نظام التلميحات")
    root.geometry("400x300")
    
    # قائمة تجريبية
    test_list = ["أحمد محمد", "أحمد علي", "محمد أحمد", "علي محمد", "فاطمة أحمد"]
    
    # حقل مع تلميحات
    entry = AutoCompleteEntry(root, autocomplete_list=test_list)
    entry.pack(pady=10, padx=10, fill=tk.X)
    
    # قائمة منسدلة مع تلميحات
    combo = AutoCompleteCombobox(root, autocomplete_list=test_list)
    combo.pack(pady=10, padx=10, fill=tk.X)
    
    # حقل بحث ذكي
    search = SmartSearchEntry(root, search_callback=lambda x: print(f"البحث: {x}"))
    search.pack(pady=10, padx=10, fill=tk.X)
    
    root.mainloop()
