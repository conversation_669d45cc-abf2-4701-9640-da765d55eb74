# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية لبرنامج محاسبة الصيدلية الزراعية
"""

import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.database import PharmacyDatabase

def add_sample_data():
    """إضافة بيانات تجريبية للبرنامج"""
    
    print("جاري إضافة البيانات التجريبية...")
    
    # إنشاء اتصال بقاعدة البيانات
    db = PharmacyDatabase()
    
    try:
        # إضافة موردين تجريبيين
        print("إضافة الموردين...")
        supplier1_id = db.add_supplier(
            name="شركة المبيدات الزراعية",
            phone="0501234567",
            address="الرياض، المملكة العربية السعودية",
            email="<EMAIL>"
        )
        
        supplier2_id = db.add_supplier(
            name="مؤسسة الأسمدة الحديثة",
            phone="0507654321",
            address="جدة، المملكة العربية السعودية",
            email="<EMAIL>"
        )
        
        # إضافة زبائن تجريبيين
        print("إضافة الزبائن...")
        customer1_id = db.add_customer(
            name="مزرعة الخير",
            phone="0551234567",
            address="القصيم، المملكة العربية السعودية",
            email="<EMAIL>",
            credit_limit=10000.0
        )
        
        customer2_id = db.add_customer(
            name="مزرعة النخيل",
            phone="0557654321",
            address="الأحساء، المملكة العربية السعودية",
            credit_limit=15000.0
        )
        
        customer3_id = db.add_customer(
            name="أحمد المزارع",
            phone="0559876543",
            address="حائل، المملكة العربية السعودية",
            credit_limit=5000.0
        )
        
        # إضافة منتجات تجريبية
        print("إضافة المنتجات...")
        
        # مبيدات حشرية
        db.add_product(
            name="مبيد الحشرات الفعال",
            category="مبيدات حشرية",
            unit="لتر",
            purchase_price=45.0,
            selling_price=60.0,
            stock_quantity=50,
            min_stock_level=10,
            supplier_id=supplier1_id
        )
        
        db.add_product(
            name="مبيد النمل الأبيض",
            category="مبيدات حشرية",
            unit="كيلو",
            purchase_price=80.0,
            selling_price=100.0,
            stock_quantity=25,
            min_stock_level=5,
            supplier_id=supplier1_id
        )
        
        # مبيدات فطرية
        db.add_product(
            name="مبيد الفطريات المتقدم",
            category="مبيدات فطرية",
            unit="لتر",
            purchase_price=55.0,
            selling_price=75.0,
            stock_quantity=30,
            min_stock_level=8,
            supplier_id=supplier1_id
        )
        
        # أسمدة
        db.add_product(
            name="سماد NPK متوازن",
            category="أسمدة",
            unit="كيس",
            purchase_price=120.0,
            selling_price=150.0,
            stock_quantity=100,
            min_stock_level=20,
            supplier_id=supplier2_id
        )
        
        db.add_product(
            name="سماد اليوريا",
            category="أسمدة",
            unit="كيس",
            purchase_price=90.0,
            selling_price=110.0,
            stock_quantity=75,
            min_stock_level=15,
            supplier_id=supplier2_id
        )
        
        db.add_product(
            name="سماد عضوي طبيعي",
            category="أسمدة",
            unit="كيس",
            purchase_price=60.0,
            selling_price=80.0,
            stock_quantity=40,
            min_stock_level=10,
            supplier_id=supplier2_id
        )
        
        # بذور
        db.add_product(
            name="بذور طماطم هجين",
            category="بذور",
            unit="عبوة",
            purchase_price=25.0,
            selling_price=35.0,
            stock_quantity=200,
            min_stock_level=50,
            supplier_id=supplier2_id
        )
        
        db.add_product(
            name="بذور خيار مقاوم",
            category="بذور",
            unit="عبوة",
            purchase_price=20.0,
            selling_price=30.0,
            stock_quantity=150,
            min_stock_level=30,
            supplier_id=supplier2_id
        )
        
        # أدوات زراعية
        db.add_product(
            name="مرش يدوي 5 لتر",
            category="أدوات زراعية",
            unit="قطعة",
            purchase_price=85.0,
            selling_price=120.0,
            stock_quantity=15,
            min_stock_level=3,
            supplier_id=supplier1_id
        )
        
        db.add_product(
            name="قفازات زراعية مقاومة",
            category="أدوات زراعية",
            unit="زوج",
            purchase_price=12.0,
            selling_price=18.0,
            stock_quantity=80,
            min_stock_level=20,
            supplier_id=supplier1_id
        )
        
        # منتجات منخفضة المخزون للاختبار
        db.add_product(
            name="مبيد الآفات السريع",
            category="مبيدات حشرية",
            unit="لتر",
            purchase_price=70.0,
            selling_price=95.0,
            stock_quantity=3,  # أقل من الحد الأدنى
            min_stock_level=10,
            supplier_id=supplier1_id
        )
        
        db.add_product(
            name="سماد فوسفاتي",
            category="أسمدة",
            unit="كيس",
            purchase_price=100.0,
            selling_price=130.0,
            stock_quantity=0,  # نفد من المخزون
            min_stock_level=5,
            supplier_id=supplier2_id
        )
        
        print("✅ تم إضافة البيانات التجريبية بنجاح!")
        print("\nتم إضافة:")
        print("- 2 مورد")
        print("- 3 زبائن") 
        print("- 12 منتج في فئات مختلفة")
        print("- منتجات منخفضة المخزون للاختبار")
        print("\nيمكنك الآن تشغيل البرنامج واستكشاف المميزات!")
        
    except Exception as e:
        print(f"❌ حدث خطأ أثناء إضافة البيانات: {str(e)}")

if __name__ == "__main__":
    add_sample_data()
