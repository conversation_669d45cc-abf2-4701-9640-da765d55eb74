# -*- coding: utf-8 -*-
"""
تشغيل آمن لبرنامج محاسبة الصيدلية الزراعية
"""

import sys
import os
import traceback
import tkinter as tk
from tkinter import messagebox
import subprocess

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 7):
        messagebox.showerror(
            "خطأ في الإصدار",
            f"يتطلب البرنامج Python 3.7 أو أحدث\n"
            f"الإصدار الحالي: {sys.version}"
        )
        return False
    return True

def check_dependencies():
    """التحقق من المتطلبات"""
    missing_modules = []
    
    # التحقق من tkinter (مدمج عادة)
    try:
        import tkinter
    except ImportError:
        missing_modules.append("tkinter")
    
    # التحقق من sqlite3 (مدمج عادة)
    try:
        import sqlite3
    except ImportError:
        missing_modules.append("sqlite3")
    
    # التحقق من reportlab (اختياري)
    try:
        import reportlab
    except ImportError:
        print("⚠️ تحذير: مكتبة reportlab غير متوفرة. لن تعمل التقارير PDF.")
        print("لتثبيتها: pip install reportlab")
    
    if missing_modules:
        messagebox.showerror(
            "مكتبات مفقودة",
            f"المكتبات التالية مفقودة:\n{', '.join(missing_modules)}\n\n"
            f"يرجى تثبيت Python بشكل صحيح."
        )
        return False
    
    return True

def check_files():
    """التحقق من وجود الملفات المطلوبة"""
    required_files = [
        "main.py",
        "config/settings.py",
        "database/database.py",
        "database/models.py",
        "gui/main_window.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        messagebox.showerror(
            "ملفات مفقودة",
            f"الملفات التالية مفقودة:\n{chr(10).join(missing_files)}\n\n"
            f"يرجى التأكد من سلامة ملفات البرنامج."
        )
        return False
    
    return True

def create_folders():
    """إنشاء المجلدات المطلوبة"""
    folders = ["backups", "reports", "config"]
    
    for folder in folders:
        try:
            os.makedirs(folder, exist_ok=True)
        except Exception as e:
            print(f"تحذير: فشل في إنشاء مجلد {folder}: {e}")

def backup_database():
    """إنشاء نسخة احتياطية تلقائية عند البدء"""
    try:
        if os.path.exists("pharmacy_accounting.db"):
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backups/auto_backup_{timestamp}.db"
            
            import shutil
            shutil.copy2("pharmacy_accounting.db", backup_name)
            print(f"✅ تم إنشاء نسخة احتياطية تلقائية: {backup_name}")
            
            # حذف النسخ القديمة (الاحتفاظ بآخر 5 نسخ)
            import glob
            backup_files = sorted(glob.glob("backups/auto_backup_*.db"))
            if len(backup_files) > 5:
                for old_backup in backup_files[:-5]:
                    try:
                        os.remove(old_backup)
                        print(f"🗑️ تم حذف النسخة القديمة: {old_backup}")
                    except:
                        pass
                        
    except Exception as e:
        print(f"تحذير: فشل في إنشاء النسخة الاحتياطية التلقائية: {e}")

def run_program():
    """تشغيل البرنامج الرئيسي"""
    try:
        print("🚀 بدء تشغيل برنامج محاسبة الصيدلية الزراعية...")
        
        # إضافة المسار الحالي لـ Python
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        # استيراد وتشغيل البرنامج الرئيسي
        from main import main
        main()
        
    except ImportError as e:
        messagebox.showerror(
            "خطأ في الاستيراد",
            f"فشل في استيراد البرنامج الرئيسي:\n{str(e)}\n\n"
            f"يرجى التأكد من سلامة ملفات البرنامج."
        )
        return False
        
    except Exception as e:
        error_msg = f"خطأ غير متوقع:\n{str(e)}\n\nتفاصيل الخطأ:\n{traceback.format_exc()}"
        
        # محاولة عرض الخطأ في نافذة
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("خطأ في البرنامج", error_msg)
            root.destroy()
        except:
            # إذا فشل عرض النافذة، اطبع في الكونسول
            print("❌ خطأ في البرنامج:")
            print(error_msg)
        
        return False
    
    return True

def show_startup_info():
    """عرض معلومات البدء"""
    print("=" * 60)
    print("🏥 برنامج محاسبة الصيدلية الزراعية")
    print("=" * 60)
    print(f"📍 مجلد البرنامج: {os.getcwd()}")
    print(f"🐍 إصدار Python: {sys.version}")
    print(f"💻 نظام التشغيل: {os.name}")
    print("=" * 60)

def main():
    """الدالة الرئيسية"""
    # عرض معلومات البدء
    show_startup_info()
    
    # التحقق من المتطلبات
    print("🔍 فحص المتطلبات...")
    
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return False
    
    if not check_dependencies():
        input("اضغط Enter للخروج...")
        return False
    
    if not check_files():
        input("اضغط Enter للخروج...")
        return False
    
    print("✅ جميع المتطلبات متوفرة")
    
    # إنشاء المجلدات المطلوبة
    print("📁 إنشاء المجلدات...")
    create_folders()
    
    # إنشاء نسخة احتياطية تلقائية
    print("💾 إنشاء نسخة احتياطية تلقائية...")
    backup_database()
    
    # تشغيل البرنامج
    print("🎯 تشغيل البرنامج...")
    success = run_program()
    
    if success:
        print("✅ تم إغلاق البرنامج بنجاح")
    else:
        print("❌ تم إغلاق البرنامج مع أخطاء")
        input("اضغط Enter للخروج...")
    
    return success

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        traceback.print_exc()
        input("اضغط Enter للخروج...")
    finally:
        # تنظيف الموارد
        try:
            import gc
            gc.collect()
        except:
            pass
