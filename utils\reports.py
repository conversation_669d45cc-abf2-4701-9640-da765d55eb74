# -*- coding: utf-8 -*-
"""
نظام التقارير PDF لبرنامج محاسبة الصيدلية الزراعية
"""

# استيراد مكتبات التقارير (اختيارية)
try:
    from reportlab.lib.pagesizes import A4, letter
    from reportlab.lib import colors
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch, cm
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.pdfgen import canvas
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
from datetime import datetime
import os
import sys

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import *

class PDFReportGenerator:
    """مولد التقارير PDF"""

    def __init__(self, db):
        self.db = db
        if not REPORTLAB_AVAILABLE:
            raise ImportError("مكتبة reportlab غير متوفرة. يرجى تثبيتها باستخدام: pip install reportlab")
        self.setup_fonts()
        
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # محاولة تحميل خط عربي (يمكن تحميل خط من Google Fonts)
            # font_path = "fonts/NotoSansArabic-Regular.ttf"
            # if os.path.exists(font_path):
            #     pdfmetrics.registerFont(TTFont('Arabic', font_path))
            #     self.arabic_font = 'Arabic'
            # else:
            self.arabic_font = 'Helvetica'  # خط افتراضي
        except:
            self.arabic_font = 'Helvetica'
    
    def create_header(self, canvas, doc, title):
        """إنشاء رأس الصفحة"""
        canvas.saveState()
        
        # معلومات الشركة
        canvas.setFont(self.arabic_font, 16)
        canvas.drawCentredText(A4[0]/2, A4[1] - 50, COMPANY_NAME)
        
        canvas.setFont(self.arabic_font, 12)
        if COMPANY_ADDRESS:
            canvas.drawCentredText(A4[0]/2, A4[1] - 70, COMPANY_ADDRESS)
        if COMPANY_PHONE:
            canvas.drawCentredText(A4[0]/2, A4[1] - 90, f"هاتف: {COMPANY_PHONE}")
        
        # خط فاصل
        canvas.line(50, A4[1] - 110, A4[0] - 50, A4[1] - 110)
        
        # عنوان التقرير
        canvas.setFont(self.arabic_font, 14)
        canvas.drawCentredText(A4[0]/2, A4[1] - 140, title)
        
        # التاريخ
        canvas.setFont(self.arabic_font, 10)
        canvas.drawRightString(A4[0] - 50, A4[1] - 160, f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        
        canvas.restoreState()
    
    def create_footer(self, canvas, doc):
        """إنشاء تذييل الصفحة"""
        canvas.saveState()
        canvas.setFont(self.arabic_font, 9)
        canvas.drawCentredText(A4[0]/2, 30, f"صفحة {doc.page}")
        canvas.drawCentredText(A4[0]/2, 15, f"تم إنشاؤه بواسطة {WINDOW_TITLE}")
        canvas.restoreState()

class CustomerStatementReport(PDFReportGenerator):
    """تقرير كشف حساب الزبون"""
    
    def generate_customer_statement(self, customer_id, customer_name):
        """إنشاء كشف حساب الزبون"""
        filename = f"reports/customer_statement_{customer_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        
        doc = SimpleDocTemplate(filename, pagesize=A4,
                               rightMargin=50, leftMargin=50,
                               topMargin=200, bottomMargin=50)
        
        # محتوى التقرير
        story = []
        
        # معلومات الزبون
        customer = self.db.get_customer_by_id(customer_id)
        
        customer_info = [
            ['اسم الزبون:', customer['name']],
            ['الهاتف:', customer['phone'] or 'غير محدد'],
            ['العنوان:', customer['address'] or 'غير محدد'],
            ['الحد الائتماني:', f"{customer['credit_limit']:.2f} {CURRENCY_SYMBOL}"],
            ['الدين الحالي:', f"{customer['current_debt']:.2f} {CURRENCY_SYMBOL}"]
        ]
        
        customer_table = Table(customer_info, colWidths=[3*cm, 8*cm])
        customer_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (1, 0), (1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(customer_table)
        story.append(Spacer(1, 20))
        
        # المبيعات
        story.append(Paragraph("المبيعات:", ParagraphStyle('Heading2', fontName=self.arabic_font, fontSize=12, alignment=2)))
        story.append(Spacer(1, 10))
        
        sales_data = [['رقم البيع', 'التاريخ', 'المبلغ الإجمالي', 'المدفوع', 'المتبقي']]
        
        # تحميل مبيعات الزبون
        conn = self.db.db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id, sale_date, total_amount, paid_amount, remaining_amount
            FROM sales WHERE customer_id = ?
            ORDER BY sale_date DESC
        ''', (customer_id,))
        
        sales = cursor.fetchall()
        
        for sale in sales:
            sales_data.append([
                str(sale[0]),
                sale[1],
                f"{sale[2]:.2f}",
                f"{sale[3]:.2f}",
                f"{sale[4]:.2f}"
            ])
        
        if len(sales_data) > 1:
            sales_table = Table(sales_data, colWidths=[2*cm, 3*cm, 3*cm, 3*cm, 3*cm])
            sales_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(sales_table)
        else:
            story.append(Paragraph("لا توجد مبيعات", ParagraphStyle('Normal', fontName=self.arabic_font, fontSize=10, alignment=2)))
        
        story.append(Spacer(1, 20))
        
        # المدفوعات
        story.append(Paragraph("المدفوعات:", ParagraphStyle('Heading2', fontName=self.arabic_font, fontSize=12, alignment=2)))
        story.append(Spacer(1, 10))
        
        payments_data = [['رقم الدفعة', 'التاريخ', 'المبلغ', 'النوع', 'طريقة الدفع']]
        
        payments = self.db.get_customer_payments(customer_id)
        
        for payment in payments:
            payments_data.append([
                str(payment['id']),
                payment['payment_date'],
                f"{payment['amount']:.2f}",
                payment['payment_type'],
                payment['payment_method']
            ])
        
        if len(payments_data) > 1:
            payments_table = Table(payments_data, colWidths=[2*cm, 3*cm, 3*cm, 2*cm, 3*cm])
            payments_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(payments_table)
        else:
            story.append(Paragraph("لا توجد مدفوعات", ParagraphStyle('Normal', fontName=self.arabic_font, fontSize=10, alignment=2)))
        
        conn.close()
        
        # بناء التقرير
        def add_header_footer(canvas, doc):
            self.create_header(canvas, doc, f"كشف حساب الزبون: {customer_name}")
            self.create_footer(canvas, doc)
        
        doc.build(story, onFirstPage=add_header_footer, onLaterPages=add_header_footer)
        
        return filename

class SalesReport(PDFReportGenerator):
    """تقرير المبيعات"""
    
    def generate_daily_sales_report(self, date=None):
        """تقرير المبيعات اليومية"""
        if not date:
            date = datetime.now().strftime('%Y-%m-%d')
        
        filename = f"reports/daily_sales_{date}.pdf"
        
        doc = SimpleDocTemplate(filename, pagesize=A4,
                               rightMargin=50, leftMargin=50,
                               topMargin=200, bottomMargin=50)
        
        story = []
        
        # ملخص المبيعات
        summary = self.db.get_daily_sales_summary(date)
        
        summary_data = [
            ['عدد المبيعات:', str(summary['sales_count'])],
            ['إجمالي المبيعات:', f"{summary['total_sales']:.2f} {CURRENCY_SYMBOL}"],
            ['المبلغ المدفوع:', f"{summary['total_paid']:.2f} {CURRENCY_SYMBOL}"],
            ['المبلغ المتبقي:', f"{summary['total_remaining']:.2f} {CURRENCY_SYMBOL}"]
        ]
        
        summary_table = Table(summary_data, colWidths=[4*cm, 6*cm])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightblue),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(summary_table)
        story.append(Spacer(1, 20))
        
        # تفاصيل المبيعات
        story.append(Paragraph("تفاصيل المبيعات:", ParagraphStyle('Heading2', fontName=self.arabic_font, fontSize=12, alignment=2)))
        story.append(Spacer(1, 10))
        
        sales_data = [['رقم البيع', 'الزبون', 'المبلغ الإجمالي', 'المدفوع', 'المتبقي', 'طريقة الدفع']]
        
        sales = self.db.get_all_sales(date, date)
        
        for sale in sales:
            sales_data.append([
                str(sale['id']),
                sale['customer_name'] or 'زبون نقدي',
                f"{sale['total_amount']:.2f}",
                f"{sale['paid_amount']:.2f}",
                f"{sale['remaining_amount']:.2f}",
                sale['payment_method']
            ])
        
        if len(sales_data) > 1:
            sales_table = Table(sales_data, colWidths=[1.5*cm, 3*cm, 2.5*cm, 2.5*cm, 2.5*cm, 2*cm])
            sales_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 8),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(sales_table)
        else:
            story.append(Paragraph("لا توجد مبيعات في هذا التاريخ", ParagraphStyle('Normal', fontName=self.arabic_font, fontSize=10, alignment=2)))
        
        # بناء التقرير
        def add_header_footer(canvas, doc):
            self.create_header(canvas, doc, f"تقرير المبيعات اليومية - {date}")
            self.create_footer(canvas, doc)
        
        doc.build(story, onFirstPage=add_header_footer, onLaterPages=add_header_footer)
        
        return filename
    
    def generate_monthly_sales_report(self, year, month):
        """تقرير المبيعات الشهرية"""
        filename = f"reports/monthly_sales_{year}_{month:02d}.pdf"
        
        doc = SimpleDocTemplate(filename, pagesize=A4,
                               rightMargin=50, leftMargin=50,
                               topMargin=200, bottomMargin=50)
        
        story = []
        
        # ملخص المبيعات الشهرية
        summary = self.db.get_monthly_sales_report(year, month)
        
        summary_data = [
            ['عدد المبيعات:', str(summary['sales_count'])],
            ['إجمالي المبيعات:', f"{summary['total_sales']:.2f} {CURRENCY_SYMBOL}"],
            ['المبلغ المدفوع:', f"{summary['total_paid']:.2f} {CURRENCY_SYMBOL}"],
            ['المبلغ المتبقي:', f"{summary['total_remaining']:.2f} {CURRENCY_SYMBOL}"],
            ['متوسط البيع:', f"{summary['average_sale']:.2f} {CURRENCY_SYMBOL}"]
        ]
        
        summary_table = Table(summary_data, colWidths=[4*cm, 6*cm])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgreen),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(summary_table)
        story.append(Spacer(1, 20))
        
        # أكثر المنتجات مبيعاً
        story.append(Paragraph("أكثر المنتجات مبيعاً:", ParagraphStyle('Heading2', fontName=self.arabic_font, fontSize=12, alignment=2)))
        story.append(Spacer(1, 10))
        
        top_products = self.db.get_top_selling_products(10)
        
        if top_products:
            products_data = [['المنتج', 'الفئة', 'الكمية المباعة', 'إجمالي الإيرادات']]
            
            for product in top_products:
                products_data.append([
                    product['name'],
                    product['category'],
                    f"{product['total_sold']} {product['unit']}",
                    f"{product['total_revenue']:.2f} {CURRENCY_SYMBOL}"
                ])
            
            products_table = Table(products_data, colWidths=[4*cm, 3*cm, 3*cm, 4*cm])
            products_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(products_table)
        else:
            story.append(Paragraph("لا توجد بيانات مبيعات", ParagraphStyle('Normal', fontName=self.arabic_font, fontSize=10, alignment=2)))
        
        # بناء التقرير
        def add_header_footer(canvas, doc):
            self.create_header(canvas, doc, f"تقرير المبيعات الشهرية - {year}/{month:02d}")
            self.create_footer(canvas, doc)
        
        doc.build(story, onFirstPage=add_header_footer, onLaterPages=add_header_footer)
        
        return filename

class InventoryReport(PDFReportGenerator):
    """تقرير المخزون"""
    
    def generate_inventory_report(self):
        """تقرير المخزون الشامل"""
        filename = f"reports/inventory_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        
        doc = SimpleDocTemplate(filename, pagesize=A4,
                               rightMargin=50, leftMargin=50,
                               topMargin=200, bottomMargin=50)
        
        story = []
        
        # ملخص المخزون
        products = self.db.get_all_products()
        low_stock_products = self.db.get_low_stock_products()
        
        total_value = sum(p['purchase_price'] * p['stock_quantity'] for p in products)
        
        summary_data = [
            ['إجمالي المنتجات:', str(len(products))],
            ['القيمة الإجمالية:', f"{total_value:.2f} {CURRENCY_SYMBOL}"],
            ['منتجات منخفضة المخزون:', str(len(low_stock_products))],
            ['منتجات نفدت:', str(len([p for p in products if p['stock_quantity'] == 0]))]
        ]
        
        summary_table = Table(summary_data, colWidths=[4*cm, 6*cm])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightyellow),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(summary_table)
        story.append(Spacer(1, 20))
        
        # المنتجات منخفضة المخزون
        if low_stock_products:
            story.append(Paragraph("المنتجات منخفضة المخزون:", ParagraphStyle('Heading2', fontName=self.arabic_font, fontSize=12, alignment=2)))
            story.append(Spacer(1, 10))
            
            low_stock_data = [['المنتج', 'الفئة', 'الكمية الحالية', 'الحد الأدنى', 'المورد']]
            
            for product in low_stock_products:
                low_stock_data.append([
                    product['name'],
                    product['category'],
                    f"{product['stock_quantity']} {product['unit']}",
                    f"{product['min_stock_level']} {product['unit']}",
                    product['supplier_name'] or 'غير محدد'
                ])
            
            low_stock_table = Table(low_stock_data, colWidths=[3*cm, 2.5*cm, 2.5*cm, 2.5*cm, 3.5*cm])
            low_stock_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.red),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 8),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.mistyrose),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(low_stock_table)
        
        # بناء التقرير
        def add_header_footer(canvas, doc):
            self.create_header(canvas, doc, "تقرير المخزون الشامل")
            self.create_footer(canvas, doc)
        
        doc.build(story, onFirstPage=add_header_footer, onLaterPages=add_header_footer)
        
        return filename
