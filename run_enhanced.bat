@echo off
chcp 65001 > nul
title برنامج محاسبة الصيدلية الزراعية - التشغيل المحسن
color 0A

echo.
echo ========================================
echo    برنامج محاسبة الصيدلية الزراعية
echo         التشغيل المحسن والآمن
echo ========================================
echo.

REM التحقق من وجود Python
echo 🔍 فحص Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo.
    echo يرجى تثبيت Python 3.7 أو أحدث من:
    echo https://python.org
    echo.
    echo تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ تم العثور على Python %PYTHON_VERSION%
echo.

REM التحقق من وجود الملفات المطلوبة
echo 🔍 فحص الملفات المطلوبة...

if not exist "main.py" (
    echo ❌ ملف main.py غير موجود
    echo يرجى التأكد من وجود جميع ملفات البرنامج
    pause
    exit /b 1
)

if not exist "config\settings.py" (
    echo ❌ ملف الإعدادات غير موجود
    echo يرجى التأكد من وجود مجلد config وملفاته
    pause
    exit /b 1
)

if not exist "database\database.py" (
    echo ❌ ملفات قاعدة البيانات غير موجودة
    echo يرجى التأكد من وجود مجلد database وملفاته
    pause
    exit /b 1
)

echo ✅ جميع الملفات المطلوبة موجودة
echo.

REM إنشاء المجلدات المطلوبة
echo 📁 إنشاء المجلدات المطلوبة...
if not exist "backups" mkdir backups
if not exist "reports" mkdir reports
if not exist "config" mkdir config
echo ✅ تم إنشاء المجلدات
echo.

REM التحقق من المتطلبات
echo 🔍 فحص المتطلبات...
python -c "import tkinter, sqlite3" > nul 2>&1
if errorlevel 1 (
    echo ❌ بعض المكتبات المطلوبة غير متوفرة
    echo يرجى تثبيت Python بشكل كامل
    pause
    exit /b 1
)

echo ✅ جميع المتطلبات الأساسية متوفرة
echo.

REM فحص المكتبات الاختيارية
echo 🔍 فحص المكتبات الاختيارية...
python -c "import reportlab" > nul 2>&1
if errorlevel 1 (
    echo ⚠️ مكتبة reportlab غير متوفرة
    echo التقارير PDF لن تعمل
    echo لتثبيتها: pip install reportlab
) else (
    echo ✅ مكتبة reportlab متوفرة
)
echo.

REM اختيار طريقة التشغيل
if exist "run_safe.py" (
    echo 🚀 تشغيل البرنامج بالوضع الآمن...
    python run_safe.py
) else (
    echo 🚀 تشغيل البرنامج بالوضع العادي...
    python main.py
)

REM التحقق من نتيجة التشغيل
if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل البرنامج
    echo.
    echo الحلول المقترحة:
    echo 1. تأكد من تثبيت Python بشكل صحيح
    echo 2. شغل: pip install -r requirements.txt
    echo 3. تأكد من سلامة ملفات البرنامج
    echo 4. أعد تشغيل الكمبيوتر وحاول مرة أخرى
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق البرنامج بنجاح
    echo شكراً لاستخدام برنامج محاسبة الصيدلية الزراعية
    echo.
    timeout /t 3 > nul
)

exit /b 0
