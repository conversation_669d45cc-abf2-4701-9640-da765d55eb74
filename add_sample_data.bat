@echo off
echo ========================================
echo    إضافة بيانات تجريبية للبرنامج
echo ========================================
echo.
echo سيتم إضافة بيانات تجريبية تشمل:
echo - موردين
echo - زبائن
echo - منتجات متنوعة
echo - منتجات منخفضة المخزون للاختبار
echo.
echo هذا سيساعدك على فهم كيفية عمل البرنامج
echo.

set /p choice="هل تريد المتابعة؟ (y/n): "
if /i "%choice%"=="y" goto continue
if /i "%choice%"=="yes" goto continue
goto end

:continue
echo.
echo جاري إضافة البيانات التجريبية...
python add_sample_data.py

if errorlevel 1 (
    echo.
    echo حدث خطأ أثناء إضافة البيانات
    echo تأكد من تثبيت المتطلبات أولاً بتشغيل install.bat
) else (
    echo.
    echo تم بنجاح! يمكنك الآن تشغيل البرنامج
)

:end
echo.
pause
