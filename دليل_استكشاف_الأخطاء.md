# دليل استكشاف الأخطاء وإصلاحها - محدث

## 🎯 **حالة النظام الحالية: ✅ جميع العمليات تعمل بنجاح**

### 📊 **نتائج الاختبار الشامل:**
- ✅ استيراد الوحدات: نجح
- ✅ عمليات قاعدة البيانات: نجح
- ✅ إنشاء النوافذ: نجح
- ✅ البيع بالدين: يعمل
- ✅ ترحيل الديون: يعمل
- ✅ تحديث المخزون: يعمل

## 🔧 المشاكل الشائعة والحلول

### 1. مشاكل التثبيت والتشغيل

#### ❌ "Python غير مثبت أو غير موجود في PATH"
**الحل:**
1. تحميل Python من [python.org](https://python.org)
2. أثناء التثبيت، تأكد من تحديد "Add Python to PATH"
3. أعد تشغيل الكمبيوتر
4. تأكد من التثبيت: `python --version`

#### ❌ "ModuleNotFoundError: No module named 'tkinter'"
**الحل:**
- **Windows:** أعد تثبيت Python مع تحديد "tcl/tk and IDLE"
- **Linux:** `sudo apt-get install python3-tk`
- **Mac:** `brew install python-tk`

#### ❌ "database is locked"
**الحل:**
1. أغلق جميع نوافذ البرنامج
2. أعد تشغيل البرنامج
3. إذا استمرت المشكلة، أعد تشغيل الكمبيوتر
4. استعد نسخة احتياطية إذا لزم الأمر

### 2. مشاكل البيع والديون (تم إصلاحها ✅)

#### ❌ "فشل في حفظ البيع"
**الحل المطبق:**
- ✅ تم إصلاح دالة save_sale مع تسجيل مفصل
- ✅ تم تحسين معالجة الأخطاء
- ✅ تم إضافة رسائل تشخيصية واضحة

#### ❌ "البيع بالدين لا يعمل"
**الحل المطبق:**
- ✅ تم إصلاح دالة show_payment_options
- ✅ تم إصلاح ترحيل الديون التلقائي
- ✅ تم إصلاح فتح نافذة تفاصيل الزبون

#### ❌ "الديون لا تظهر في صفحة الزبون"
**الحل المطبق:**
- ✅ تم إصلاح دالة update_customer_debt
- ✅ تم إصلاح دالة get_customer_debt
- ✅ تم تحسين عرض الديون في نافذة الزبون

### 3. مشاكل قاعدة البيانات

#### ❌ "فشل في تحميل البيانات"
**الحل:**
1. تحقق من وجود ملف `pharmacy_accounting.db`
2. تحقق من صلاحيات الملف (قراءة/كتابة)
3. استعد نسخة احتياطية من مجلد `backups`
4. شغل فحص سلامة البيانات من الإعدادات

#### ❌ "خطأ في إنشاء قاعدة البيانات"
**الحل:**
1. تأكد من وجود مساحة كافية على القرص الصلب
2. تأكد من صلاحيات الكتابة في مجلد البرنامج
3. أغلق برامج مكافحة الفيروسات مؤقتاً
4. شغل البرنامج كمدير (Run as Administrator)

### 3. مشاكل الواجهة

#### ❌ "النوافذ لا تظهر بشكل صحيح"
**الحل:**
1. تحقق من دقة الشاشة (1024x768 أو أعلى)
2. تحقق من إعدادات العرض (100% أو 125%)
3. أعد تشغيل البرنامج
4. غير حجم الخط من الإعدادات

#### ❌ "النص يظهر بشكل غريب أو مقطع"
**الحل:**
1. تأكد من دعم النظام للغة العربية
2. ثبت خطوط عربية إضافية
3. غير حجم الخط من الإعدادات
4. تحقق من إعدادات اللغة في Windows

### 4. مشاكل التقارير

#### ❌ "فشل في إنشاء التقارير PDF"
**الحل:**
1. ثبت مكتبة reportlab: `pip install reportlab`
2. تأكد من وجود مجلد `reports`
3. تحقق من صلاحيات الكتابة
4. أغلق ملفات PDF المفتوحة

#### ❌ "التقارير فارغة أو ناقصة"
**الحل:**
1. تأكد من وجود بيانات في قاعدة البيانات
2. تحقق من فلاتر التاريخ
3. أعد تشغيل البرنامج
4. استعد نسخة احتياطية

### 5. مشاكل النسخ الاحتياطية

#### ❌ "فشل في إنشاء النسخة الاحتياطية"
**الحل:**
1. تأكد من وجود مساحة كافية
2. تحقق من صلاحيات الكتابة في مجلد `backups`
3. أغلق جميع نوافذ البرنامج
4. شغل البرنامج كمدير

#### ❌ "فشل في استعادة النسخة الاحتياطية"
**الحل:**
1. تأكد من سلامة ملف النسخة الاحتياطية
2. أغلق البرنامج تماماً قبل الاستعادة
3. انسخ ملف النسخة يدوياً إذا لزم الأمر
4. أعد تشغيل البرنامج

## 🛠️ أدوات التشخيص

### 1. اختبار النظام
```bash
# تشغيل اختبار شامل للنظام
python test_system.py
```

### 2. فحص قاعدة البيانات
- من الإعدادات → إعدادات متقدمة → فحص سلامة البيانات

### 3. تحسين الأداء
- من الإعدادات → إعدادات متقدمة → تحسين قاعدة البيانات

### 4. معلومات النظام
- من قائمة مساعدة → حول البرنامج

## 📞 الحصول على المساعدة

### 1. معلومات مفيدة للدعم
عند طلب المساعدة، يرجى تقديم:
- إصدار Python: `python --version`
- نظام التشغيل ونسخته
- رسالة الخطأ الكاملة
- الخطوات التي أدت للمشكلة
- لقطة شاشة إذا أمكن

### 2. ملفات السجلات
- تحقق من وجود ملفات سجلات في مجلد البرنامج
- ابحث عن ملفات `.log` أو `.txt`

### 3. معلومات قاعدة البيانات
- حجم ملف `pharmacy_accounting.db`
- تاريخ آخر تعديل
- عدد السجلات (من الإعدادات المتقدمة)

## 🔄 إعادة تعيين البرنامج

### إعادة تعيين كاملة (احذر: ستفقد جميع البيانات)
1. أنشئ نسخة احتياطية أولاً
2. احذف ملف `pharmacy_accounting.db`
3. احذف مجلد `config` (الإعدادات)
4. شغل البرنامج لإنشاء قاعدة بيانات جديدة

### إعادة تعيين الإعدادات فقط
1. احذف ملف `config/user_settings.json`
2. أعد تشغيل البرنامج

## 🚨 في حالة الطوارئ

### استعادة سريعة
1. أغلق البرنامج تماماً
2. انسخ آخر نسخة احتياطية من `backups`
3. أعد تسميتها إلى `pharmacy_accounting.db`
4. شغل البرنامج

### نسخة احتياطية يدوية سريعة
1. انسخ ملف `pharmacy_accounting.db`
2. ضعه في مكان آمن مع التاريخ في الاسم
3. مثال: `pharmacy_backup_2024_12_15.db`

## 📋 قائمة فحص المشاكل

- [ ] Python مثبت ويعمل
- [ ] جميع الملفات المطلوبة موجودة
- [ ] صلاحيات القراءة/الكتابة متوفرة
- [ ] مساحة كافية على القرص الصلب
- [ ] لا توجد برامج تتداخل مع البرنامج
- [ ] آخر نسخة احتياطية متوفرة
- [ ] إعدادات النظام صحيحة

## 💡 نصائح لتجنب المشاكل

1. **أنشئ نسخ احتياطية منتظمة**
2. **لا تحذف ملفات البرنامج يدوياً**
3. **أغلق البرنامج بشكل صحيح**
4. **تأكد من تحديث النظام**
5. **استخدم التشغيل الآمن عند الشك**
6. **احتفظ بنسخة من البيانات في مكان آمن**

---

**ملاحظة:** إذا لم تجد حلاً لمشكلتك هنا، جرب التشغيل الآمن `python run_safe.py` أو الاختبار الشامل `python test_system.py`
