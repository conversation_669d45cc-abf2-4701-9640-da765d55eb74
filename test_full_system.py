# -*- coding: utf-8 -*-
"""
اختبار شامل للنظام - جميع العمليات
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار استيراد جميع الوحدات"""
    print("🔄 اختبار استيراد الوحدات...")
    
    try:
        # اختبار استيراد قاعدة البيانات
        from database.database import PharmacyDatabase
        print("✅ تم استيراد قاعدة البيانات")
        
        # اختبار استيراد الإعدادات
        import config.settings
        print("✅ تم استيراد الإعدادات")
        
        # اختبار استيراد النوافذ
        from gui.main_window import MainWindow
        print("✅ تم استيراد النافذة الرئيسية")
        
        from gui.sales import SalesWindow, NewSaleWindow
        print("✅ تم استيراد نوافذ المبيعات")
        
        from gui.customers import CustomersWindow, CustomerDetailsWindow
        print("✅ تم استيراد نوافذ الزبائن")
        
        from gui.suppliers import SuppliersWindow
        print("✅ تم استيراد نوافذ الموردين")
        
        from gui.inventory import InventoryWindow
        print("✅ تم استيراد نوافذ المخزون")
        
        from gui.purchases import PurchasesWindow
        print("✅ تم استيراد نوافذ المشتريات")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في الاستيراد: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_operations():
    """اختبار عمليات قاعدة البيانات"""
    print("\n🔄 اختبار عمليات قاعدة البيانات...")
    
    try:
        from database.database import PharmacyDatabase
        
        # إنشاء قاعدة بيانات اختبار
        db = PharmacyDatabase("test_full_system.db")
        print("✅ تم إنشاء قاعدة البيانات")
        
        # اختبار إضافة مورد
        supplier_id = db.add_supplier(
            name="مورد اختبار",
            phone="0501234567",
            address="الرياض"
        )
        print(f"✅ تم إضافة مورد: {supplier_id}")
        
        # اختبار إضافة زبون
        customer_id = db.add_customer(
            name="زبون اختبار",
            phone="0509876543",
            address="جدة",
            credit_limit=1000.0
        )
        print(f"✅ تم إضافة زبون: {customer_id}")
        
        # اختبار إضافة منتج
        product_id = db.add_product(
            name="منتج اختبار",
            category="أدوية",
            unit="حبة",
            purchase_price=5.0,
            selling_price=10.0,
            stock_quantity=100,
            min_stock_level=10,
            supplier_id=supplier_id
        )
        print(f"✅ تم إضافة منتج: {product_id}")
        
        # اختبار البيع
        sale_id = db.add_sale(
            customer_id=customer_id,
            total_amount=50.0,
            paid_amount=0.0,
            payment_method="دين",
            notes="بيع اختبار"
        )
        print(f"✅ تم إضافة بيع: {sale_id}")
        
        # اختبار إضافة عنصر بيع
        item_id = db.add_sale_item(
            sale_id=sale_id,
            product_id=product_id,
            quantity=5,
            unit_price=10.0
        )
        print(f"✅ تم إضافة عنصر بيع: {item_id}")
        
        # اختبار تحديث دين الزبون
        db.update_customer_debt(customer_id, 50.0)
        print("✅ تم تحديث دين الزبون")
        
        # التحقق من البيانات
        customer = db.get_customer_by_id(customer_id)
        print(f"✅ دين الزبون: {customer['current_debt']}")
        
        product = db.get_product_by_id(product_id)
        print(f"✅ مخزون المنتج: {product['stock_quantity']}")
        
        # تنظيف
        os.remove("test_full_system.db")
        print("✅ تم حذف قاعدة بيانات الاختبار")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في اختبار قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        
        try:
            os.remove("test_full_system.db")
        except:
            pass
        
        return False

def test_gui_creation():
    """اختبار إنشاء النوافذ"""
    print("\n🔄 اختبار إنشاء النوافذ...")
    
    try:
        from database.database import PharmacyDatabase
        from gui.sales import SalesWindow, NewSaleWindow
        from gui.customers import CustomersWindow
        
        # إنشاء قاعدة بيانات مؤقتة
        db = PharmacyDatabase("test_gui.db")
        
        # إنشاء نافذة جذر مؤقتة
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الجذر
        
        # اختبار نافذة المبيعات
        sales_window = SalesWindow(root)
        print("✅ تم إنشاء نافذة المبيعات")
        sales_window.window.destroy()
        
        # اختبار نافذة الزبائن
        customers_window = CustomersWindow(root)
        print("✅ تم إنشاء نافذة الزبائن")
        customers_window.window.destroy()
        
        # تنظيف
        root.destroy()
        os.remove("test_gui.db")
        print("✅ تم تنظيف اختبار النوافذ")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في اختبار النوافذ: {e}")
        import traceback
        traceback.print_exc()
        
        try:
            os.remove("test_gui.db")
        except:
            pass
        
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 بدء الاختبار الشامل للنظام...")
    print("=" * 50)
    
    # اختبار الاستيراد
    imports_ok = test_imports()
    
    # اختبار قاعدة البيانات
    database_ok = test_database_operations()
    
    # اختبار النوافذ
    gui_ok = test_gui_creation()
    
    print("\n" + "=" * 50)
    print("📊 ملخص نتائج الاختبار:")
    print(f"   استيراد الوحدات: {'✅ نجح' if imports_ok else '❌ فشل'}")
    print(f"   عمليات قاعدة البيانات: {'✅ نجح' if database_ok else '❌ فشل'}")
    print(f"   إنشاء النوافذ: {'✅ نجح' if gui_ok else '❌ فشل'}")
    
    if imports_ok and database_ok and gui_ok:
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        return True
    else:
        print("\n❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🚀 يمكنك الآن تشغيل البرنامج بأمان:")
        print("   python main.py")
    else:
        print("\n🔧 يرجى إصلاح الأخطاء قبل تشغيل البرنامج")
    
    input("\nاضغط Enter للخروج...")
