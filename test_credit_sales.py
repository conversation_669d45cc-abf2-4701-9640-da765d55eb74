# -*- coding: utf-8 -*-
"""
اختبار البيع بالدين وتسجيل الديون
"""

import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.database import PharmacyDatabase

def test_credit_sales():
    """اختبار البيع بالدين وتسجيل الديون"""
    print("🧪 اختبار البيع بالدين وتسجيل الديون...")
    
    try:
        # إنشاء قاعدة بيانات اختبار
        db = PharmacyDatabase("test_credit_sales.db")
        print("✅ تم إنشاء قاعدة البيانات بنجاح")
        
        # إضافة مورد
        supplier_id = db.add_supplier(
            name="مورد اختبار",
            phone="0501234567",
            address="الرياض"
        )
        print(f"✅ تم إضافة مورد بالمعرف: {supplier_id}")
        
        # إضافة منتج
        product_id = db.add_product(
            name="دواء اختبار",
            category="أدوية",
            unit="حبة",
            purchase_price=5.0,
            selling_price=10.0,
            stock_quantity=100,
            min_stock_level=10,
            supplier_id=supplier_id
        )
        print(f"✅ تم إضافة منتج بالمعرف: {product_id}")
        
        # إضافة زبون
        customer_id = db.add_customer(
            name="زبون اختبار",
            phone="0509876543",
            address="جدة",
            credit_limit=1000.0
        )
        print(f"✅ تم إضافة زبون بالمعرف: {customer_id}")
        
        # التحقق من الدين الأولي
        initial_debt = db.get_customer_debt(customer_id)
        print(f"✅ الدين الأولي للزبون: {initial_debt}")
        
        # إضافة بيع بالدين (مبلغ كامل)
        print("\n📝 اختبار بيع بالدين كامل...")
        sale_id = db.add_sale(
            customer_id=customer_id,
            total_amount=100.0,
            paid_amount=0.0,  # بيع بالدين كامل
            payment_method="دين",
            notes="بيع بالدين - اختبار"
        )
        print(f"✅ تم إضافة بيع بالدين برقم: {sale_id}")
        
        # إضافة عناصر البيع
        item_id = db.add_sale_item(
            sale_id=sale_id,
            product_id=product_id,
            quantity=10,
            unit_price=10.0
        )
        print(f"✅ تم إضافة عنصر البيع برقم: {item_id}")
        
        # تحديث دين الزبون
        print("\n💰 تحديث دين الزبون...")
        db.update_customer_debt(customer_id, 100.0)  # إضافة الدين
        print("✅ تم تحديث دين الزبون")
        
        # التحقق من الدين بعد البيع
        debt_after_sale = db.get_customer_debt(customer_id)
        print(f"✅ دين الزبون بعد البيع: {debt_after_sale}")
        
        # التحقق من بيانات الزبون
        customer = db.get_customer_by_id(customer_id)
        print(f"✅ الدين الحالي في جدول الزبائن: {customer['current_debt']}")
        
        # اختبار بيع جزئي
        print("\n💳 اختبار بيع جزئي...")
        sale2_id = db.add_sale(
            customer_id=customer_id,
            total_amount=50.0,
            paid_amount=20.0,  # دفع جزئي
            payment_method="جزئي",
            notes="بيع جزئي - اختبار"
        )
        print(f"✅ تم إضافة بيع جزئي برقم: {sale2_id}")
        
        # إضافة عناصر البيع الجزئي
        item2_id = db.add_sale_item(
            sale_id=sale2_id,
            product_id=product_id,
            quantity=5,
            unit_price=10.0
        )
        print(f"✅ تم إضافة عنصر البيع الجزئي برقم: {item2_id}")
        
        # تحديث دين الزبون للبيع الجزئي
        remaining_amount = 50.0 - 20.0  # 30.0
        db.update_customer_debt(customer_id, remaining_amount)
        print(f"✅ تم إضافة {remaining_amount} للدين")
        
        # التحقق من الدين النهائي
        final_debt = db.get_customer_debt(customer_id)
        customer_final = db.get_customer_by_id(customer_id)
        print(f"✅ الدين النهائي (حساب): {final_debt}")
        print(f"✅ الدين النهائي (جدول): {customer_final['current_debt']}")
        
        # اختبار الحصول على المبيعات
        print("\n📊 اختبار جلب المبيعات...")
        sales = db.get_all_sales()
        print(f"✅ تم جلب {len(sales)} عملية بيع")
        
        for sale in sales:
            print(f"   - بيع رقم {sale['id']}: {sale['total_amount']} ريال، مدفوع: {sale['paid_amount']}, متبقي: {sale['remaining_amount']}")
        
        # اختبار الحصول على الزبائن المدينين
        print("\n👥 اختبار جلب الزبائن المدينين...")
        debtors = db.get_customers_with_debts()
        print(f"✅ تم جلب {len(debtors)} زبون مدين")
        
        for debtor in debtors:
            print(f"   - {debtor['name']}: {debtor['current_debt']} ريال")
        
        # اختبار دفعة من الزبون
        print("\n💰 اختبار دفعة من الزبون...")
        payment_id = db.add_payment(
            customer_id=customer_id,
            amount=50.0,
            payment_type="استلام",
            payment_method="نقدي",
            notes="دفعة من الزبون"
        )
        print(f"✅ تم تسجيل دفعة برقم: {payment_id}")
        
        # التحقق من الدين بعد الدفعة
        debt_after_payment = db.get_customer_debt(customer_id)
        customer_after_payment = db.get_customer_by_id(customer_id)
        print(f"✅ الدين بعد الدفعة (حساب): {debt_after_payment}")
        print(f"✅ الدين بعد الدفعة (جدول): {customer_after_payment['current_debt']}")
        
        # تنظيف ملف الاختبار
        os.remove("test_credit_sales.db")
        print("\n🗑️ تم حذف قاعدة بيانات الاختبار")
        
        print("\n🎉 جميع اختبارات البيع بالدين نجحت!")
        return True
        
    except Exception as e:
        print(f"\n❌ فشل الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # تنظيف في حالة الخطأ
        try:
            os.remove("test_credit_sales.db")
        except:
            pass
        
        return False

if __name__ == "__main__":
    test_credit_sales()
