# -*- coding: utf-8 -*-
"""
اختبار تشخيص مشاكل البيع
"""

import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.database import PharmacyDatabase

def test_sales_debug():
    """اختبار تشخيص مشاكل البيع"""
    print("🔍 اختبار تشخيص مشاكل البيع...")
    
    try:
        # إنشاء قاعدة بيانات اختبار
        db = PharmacyDatabase("test_sales_debug.db")
        print("✅ تم إنشاء قاعدة البيانات بنجاح")
        
        # إضافة مورد
        supplier_id = db.add_supplier(
            name="مورد تجريبي",
            phone="0501234567"
        )
        print(f"✅ تم إضافة مورد بالمعرف: {supplier_id}")
        
        # إضافة منتج
        product_id = db.add_product(
            name="منتج تجريبي",
            category="أدوية",
            unit="حبة",
            purchase_price=5.0,
            selling_price=10.0,
            stock_quantity=100,
            min_stock_level=10,
            supplier_id=supplier_id
        )
        print(f"✅ تم إضافة منتج بالمعرف: {product_id}")
        
        # إضافة زبون
        customer_id = db.add_customer(
            name="زبون تجريبي",
            phone="0509876543",
            credit_limit=1000.0
        )
        print(f"✅ تم إضافة زبون بالمعرف: {customer_id}")
        
        # اختبار البيع النقدي
        print("\n💰 اختبار البيع النقدي...")
        sale1_id = db.add_sale(
            customer_id=None,  # بيع نقدي
            total_amount=50.0,
            paid_amount=50.0,
            payment_method="نقدي",
            notes="بيع نقدي تجريبي"
        )
        print(f"✅ تم إنشاء بيع نقدي برقم: {sale1_id}")
        
        # إضافة عناصر البيع النقدي
        item1_id = db.add_sale_item(
            sale_id=sale1_id,
            product_id=product_id,
            quantity=5,
            unit_price=10.0
        )
        print(f"✅ تم إضافة عنصر البيع برقم: {item1_id}")
        
        # اختبار البيع بالدين
        print("\n📝 اختبار البيع بالدين...")
        sale2_id = db.add_sale(
            customer_id=customer_id,
            total_amount=100.0,
            paid_amount=0.0,  # بيع بالدين كامل
            payment_method="دين",
            notes="بيع بالدين تجريبي"
        )
        print(f"✅ تم إنشاء بيع بالدين برقم: {sale2_id}")
        
        # إضافة عناصر البيع بالدين
        item2_id = db.add_sale_item(
            sale_id=sale2_id,
            product_id=product_id,
            quantity=10,
            unit_price=10.0
        )
        print(f"✅ تم إضافة عنصر البيع بالدين برقم: {item2_id}")
        
        # تحديث دين الزبون
        db.update_customer_debt(customer_id, 100.0)
        print("✅ تم تحديث دين الزبون")
        
        # اختبار البيع الجزئي
        print("\n💳 اختبار البيع الجزئي...")
        sale3_id = db.add_sale(
            customer_id=customer_id,
            total_amount=80.0,
            paid_amount=30.0,  # دفع جزئي
            payment_method="جزئي",
            notes="بيع جزئي تجريبي"
        )
        print(f"✅ تم إنشاء بيع جزئي برقم: {sale3_id}")
        
        # إضافة عناصر البيع الجزئي
        item3_id = db.add_sale_item(
            sale_id=sale3_id,
            product_id=product_id,
            quantity=8,
            unit_price=10.0
        )
        print(f"✅ تم إضافة عنصر البيع الجزئي برقم: {item3_id}")
        
        # تحديث دين الزبون للمبلغ المتبقي
        remaining_amount = 80.0 - 30.0  # 50.0
        db.update_customer_debt(customer_id, remaining_amount)
        print(f"✅ تم إضافة {remaining_amount} للدين")
        
        # التحقق من النتائج
        print("\n📊 التحقق من النتائج...")
        
        # التحقق من المبيعات
        sales = db.get_all_sales()
        print(f"✅ إجمالي المبيعات: {len(sales)}")
        
        for sale in sales:
            customer_name = sale['customer_name'] or 'زبون نقدي'
            print(f"   - بيع رقم {sale['id']}: {customer_name} - {sale['total_amount']:.2f} ريال")
        
        # التحقق من دين الزبون
        customer = db.get_customer_by_id(customer_id)
        print(f"✅ دين الزبون: {customer['current_debt']:.2f} ريال")
        
        # التحقق من المخزون
        product = db.get_product_by_id(product_id)
        print(f"✅ مخزون المنتج: {product['stock_quantity']} {product['unit']}")
        
        # التحقق من تفاصيل المبيعات
        print("\n🔍 تفاصيل المبيعات...")
        for sale in sales:
            sale_items = db.get_sale_items(sale['id'])
            print(f"   بيع رقم {sale['id']} يحتوي على {len(sale_items)} منتج:")
            for item in sale_items:
                print(f"     - {item['product_name']}: {item['quantity']} × {item['unit_price']:.2f} = {item['total_price']:.2f}")
        
        # تنظيف ملف الاختبار
        os.remove("test_sales_debug.db")
        print("\n🗑️ تم حذف قاعدة بيانات الاختبار")
        
        print("\n🎉 جميع اختبارات البيع نجحت!")
        print("✅ البيع النقدي يعمل")
        print("✅ البيع بالدين يعمل")
        print("✅ البيع الجزئي يعمل")
        print("✅ تحديث المخزون يعمل")
        print("✅ تحديث الديون يعمل")
        
        return True
        
    except Exception as e:
        print(f"\n❌ فشل الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # تنظيف في حالة الخطأ
        try:
            os.remove("test_sales_debug.db")
        except:
            pass
        
        return False

if __name__ == "__main__":
    test_sales_debug()
