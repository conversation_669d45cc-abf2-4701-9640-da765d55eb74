# -*- coding: utf-8 -*-
"""
النافذة الرئيسية لبرنامج محاسبة الصيدلية الزراعية
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import *
from database.database import PharmacyDatabase

class MainWindow:
    """النافذة الرئيسية للبرنامج"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.db = PharmacyDatabase()
        self.setup_window()
        self.create_menu()
        self.create_main_interface()
        
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title(WINDOW_TITLE)
        self.root.geometry(WINDOW_SIZE)
        self.root.configure(bg=COLORS['background'])
        
        # تعيين الخط الافتراضي
        default_font = (FONT_FAMILY, FONT_SIZE)
        self.root.option_add("*Font", default_font)
        
        # جعل النافذة في المنتصف
        self.center_window()
        
    def center_window(self):
        """وضع النافذة في منتصف الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="نسخة احتياطية", command=self.create_backup)
        file_menu.add_command(label="استعادة نسخة احتياطية", command=self.restore_backup)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        
        # قائمة المبيعات
        sales_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المبيعات", menu=sales_menu)
        sales_menu.add_command(label="بيع جديد", command=self.open_new_sale)
        sales_menu.add_command(label="عرض المبيعات", command=self.view_sales)
        
        # قائمة المخزون
        inventory_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المخزون", menu=inventory_menu)
        inventory_menu.add_command(label="إدارة المنتجات", command=self.manage_products)
        inventory_menu.add_command(label="تقرير المخزون", command=self.inventory_report)
        
        # قائمة الزبائن
        customers_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الزبائن", menu=customers_menu)
        customers_menu.add_command(label="إدارة الزبائن", command=self.manage_customers)
        customers_menu.add_command(label="تقرير الديون", command=self.debts_report)
        
        # قائمة الموردين
        suppliers_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الموردين", menu=suppliers_menu)
        suppliers_menu.add_command(label="إدارة الموردين", command=self.manage_suppliers)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="تقرير المبيعات اليومية", command=self.daily_sales_report)
        reports_menu.add_command(label="تقرير المبيعات الشهرية", command=self.monthly_sales_report)
        reports_menu.add_command(label="تقرير الأرباح", command=self.profit_report)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
        
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # إطار العنوان
        title_frame = tk.Frame(self.root, bg=COLORS['primary'], height=80)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text=COMPANY_NAME, 
                              font=(FONT_FAMILY, 24, 'bold'),
                              bg=COLORS['primary'], fg='white')
        title_label.pack(expand=True)
        
        # إطار الأزرار الرئيسية
        buttons_frame = tk.Frame(self.root, bg=COLORS['background'])
        buttons_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # إنشاء الأزرار الرئيسية
        self.create_main_buttons(buttons_frame)
        
        # إطار المعلومات السريعة
        info_frame = tk.Frame(self.root, bg=COLORS['background'])
        info_frame.pack(fill=tk.X, padx=20, pady=10)
        
        self.create_quick_info(info_frame)
        
    def create_main_buttons(self, parent):
        """إنشاء الأزرار الرئيسية"""
        # تقسيم الأزرار إلى صفوف
        buttons_data = [
            [("بيع جديد", self.open_new_sale, COLORS['success']),
             ("إدارة المنتجات", self.manage_products, COLORS['primary']),
             ("إدارة الزبائن", self.manage_customers, COLORS['secondary'])],
            [("إدارة الموردين", self.manage_suppliers, COLORS['primary']),
             ("تقارير المبيعات", self.view_sales, COLORS['warning']),
             ("تقرير المخزون", self.inventory_report, COLORS['warning'])],
            [("النسخ الاحتياطية", self.create_backup, COLORS['text']),
             ("تقرير الديون", self.debts_report, COLORS['warning']),
             ("إعدادات", self.show_settings, COLORS['text'])]
        ]
        
        for row_idx, row_buttons in enumerate(buttons_data):
            row_frame = tk.Frame(parent, bg=COLORS['background'])
            row_frame.pack(fill=tk.X, pady=10)
            
            for col_idx, (text, command, color) in enumerate(row_buttons):
                btn = tk.Button(row_frame, text=text, command=command,
                               font=(FONT_FAMILY, 14, 'bold'),
                               bg=color, fg='white',
                               width=20, height=3,
                               relief=tk.RAISED, bd=2)
                btn.pack(side=tk.LEFT, padx=10, expand=True, fill=tk.X)
                
    def create_quick_info(self, parent):
        """إنشاء معلومات سريعة"""
        info_label = tk.Label(parent, text="معلومات سريعة",
                             font=(FONT_FAMILY, 16, 'bold'),
                             bg=COLORS['background'])
        info_label.pack(anchor=tk.W)
        
        # إطار للمعلومات
        stats_frame = tk.Frame(parent, bg=COLORS['background'])
        stats_frame.pack(fill=tk.X, pady=5)
        
        # عرض إحصائيات سريعة
        try:
            products_count = len(self.db.get_all_products())
            customers_count = len(self.db.get_all_customers())
            low_stock_count = len(self.db.get_low_stock_products())
            
            stats_text = f"عدد المنتجات: {products_count} | عدد الزبائن: {customers_count} | منتجات منخفضة المخزون: {low_stock_count}"
            
            stats_label = tk.Label(stats_frame, text=stats_text,
                                  font=(FONT_FAMILY, 12),
                                  bg=COLORS['background'])
            stats_label.pack(anchor=tk.W)
            
        except Exception as e:
            error_label = tk.Label(stats_frame, text="خطأ في تحميل البيانات",
                                  font=(FONT_FAMILY, 12),
                                  bg=COLORS['background'], fg='red')
            error_label.pack(anchor=tk.W)

    # ==================== دوال الأحداث ====================

    def open_new_sale(self):
        """فتح نافذة بيع جديد"""
        messagebox.showinfo("قريباً", "سيتم تطوير نافذة البيع قريباً")

    def view_sales(self):
        """عرض المبيعات"""
        messagebox.showinfo("قريباً", "سيتم تطوير عرض المبيعات قريباً")

    def manage_products(self):
        """إدارة المنتجات"""
        try:
            from gui.inventory import InventoryWindow
            inventory_window = InventoryWindow(self.root)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة إدارة المنتجات: {str(e)}")

    def inventory_report(self):
        """تقرير المخزون"""
        messagebox.showinfo("قريباً", "سيتم تطوير تقرير المخزون قريباً")

    def manage_customers(self):
        """إدارة الزبائن"""
        messagebox.showinfo("قريباً", "سيتم تطوير إدارة الزبائن قريباً")

    def debts_report(self):
        """تقرير الديون"""
        messagebox.showinfo("قريباً", "سيتم تطوير تقرير الديون قريباً")

    def manage_suppliers(self):
        """إدارة الموردين"""
        messagebox.showinfo("قريباً", "سيتم تطوير إدارة الموردين قريباً")

    def daily_sales_report(self):
        """تقرير المبيعات اليومية"""
        messagebox.showinfo("قريباً", "سيتم تطوير تقرير المبيعات اليومية قريباً")

    def monthly_sales_report(self):
        """تقرير المبيعات الشهرية"""
        messagebox.showinfo("قريباً", "سيتم تطوير تقرير المبيعات الشهرية قريباً")

    def profit_report(self):
        """تقرير الأرباح"""
        messagebox.showinfo("قريباً", "سيتم تطوير تقرير الأرباح قريباً")

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        messagebox.showinfo("قريباً", "سيتم تطوير النسخ الاحتياطية قريباً")

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        messagebox.showinfo("قريباً", "سيتم تطوير استعادة النسخ الاحتياطية قريباً")

    def show_settings(self):
        """عرض الإعدادات"""
        messagebox.showinfo("قريباً", "سيتم تطوير الإعدادات قريباً")

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        about_text = f"""
{WINDOW_TITLE}

برنامج محاسبة شامل للصيدلية الزراعية
يتضمن إدارة المبيعات والمخزون والزبائن والموردين

الإصدار: 1.0
تطوير: فريق التطوير
        """
        messagebox.showinfo("حول البرنامج", about_text)

    def run(self):
        """تشغيل البرنامج"""
        self.root.mainloop()

if __name__ == "__main__":
    # التأكد من وجود المجلدات المطلوبة
    ensure_folders_exist()

    # تشغيل البرنامج
    app = MainWindow()
    app.run()
