# -*- coding: utf-8 -*-
"""
النافذة الرئيسية لبرنامج محاسبة الصيدلية الزراعية
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import *
from database.database import PharmacyDatabase

class MainWindow:
    """النافذة الرئيسية للبرنامج"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.db = PharmacyDatabase()
        self.setup_window()
        self.create_menu()
        self.create_main_interface()
        
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title(WINDOW_TITLE)
        self.root.geometry(WINDOW_SIZE)
        self.root.configure(bg=COLORS['background'])
        
        # تعيين الخط الافتراضي
        default_font = (FONT_FAMILY, FONT_SIZE)
        self.root.option_add("*Font", default_font)
        
        # جعل النافذة في المنتصف
        self.center_window()
        
    def center_window(self):
        """وضع النافذة في منتصف الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="نسخة احتياطية", command=self.create_backup)
        file_menu.add_command(label="استعادة نسخة احتياطية", command=self.restore_backup)
        file_menu.add_separator()
        file_menu.add_command(label="الإعدادات", command=self.open_settings)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        
        # قائمة المبيعات
        sales_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المبيعات", menu=sales_menu)
        sales_menu.add_command(label="بيع جديد", command=self.open_new_sale)
        sales_menu.add_command(label="عرض المبيعات", command=self.view_sales)
        
        # قائمة المخزون
        inventory_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المخزون", menu=inventory_menu)
        inventory_menu.add_command(label="إدارة المنتجات", command=self.manage_products)
        inventory_menu.add_command(label="تقرير المخزون", command=self.inventory_report)
        
        # قائمة الزبائن
        customers_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الزبائن", menu=customers_menu)
        customers_menu.add_command(label="إدارة الزبائن", command=self.manage_customers)
        customers_menu.add_command(label="تقرير الديون", command=self.debts_report)
        
        # قائمة الموردين
        suppliers_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الموردين", menu=suppliers_menu)
        suppliers_menu.add_command(label="إدارة الموردين", command=self.manage_suppliers)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="تقرير المبيعات اليومية", command=self.daily_sales_report)
        reports_menu.add_command(label="تقرير المبيعات الشهرية", command=self.monthly_sales_report)
        reports_menu.add_command(label="تقرير الأرباح", command=self.profit_report)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
        
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # إطار العنوان
        title_frame = tk.Frame(self.root, bg=COLORS['primary'], height=80)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text=COMPANY_NAME, 
                              font=(FONT_FAMILY, 24, 'bold'),
                              bg=COLORS['primary'], fg='white')
        title_label.pack(expand=True)
        
        # إطار الأزرار الرئيسية
        buttons_frame = tk.Frame(self.root, bg=COLORS['background'])
        buttons_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # إنشاء الأزرار الرئيسية
        self.create_main_buttons(buttons_frame)
        
        # إطار المعلومات السريعة
        info_frame = tk.Frame(self.root, bg=COLORS['background'])
        info_frame.pack(fill=tk.X, padx=20, pady=10)
        
        self.create_quick_info(info_frame)
        
    def create_main_buttons(self, parent):
        """إنشاء الأزرار الرئيسية"""
        # تقسيم الأزرار إلى صفوف
        buttons_data = [
            [("بيع جديد", self.open_new_sale, COLORS['success']),
             ("إدارة المنتجات", self.manage_products, COLORS['primary']),
             ("إدارة الزبائن", self.manage_customers, COLORS['secondary'])],
            [("إدارة الموردين", self.manage_suppliers, COLORS['primary']),
             ("إدارة المشتريات", self.manage_purchases, COLORS['info']),
             ("تقارير المبيعات", self.view_sales, COLORS['warning'])],
            [("تقرير المخزون", self.inventory_report, COLORS['warning']),
             ("تقرير الديون", self.debts_report, COLORS['warning']),
             ("النسخ الاحتياطية", self.create_backup, COLORS['text'])],
            [("إعدادات", self.show_settings, COLORS['text']),
             ("", None, None),
             ("", None, None)]
        ]
        
        for row_idx, row_buttons in enumerate(buttons_data):
            row_frame = tk.Frame(parent, bg=COLORS['background'])
            row_frame.pack(fill=tk.X, pady=10)
            
            for col_idx, (text, command, color) in enumerate(row_buttons):
                if text and command:  # تجاهل الأزرار الفارغة
                    btn = tk.Button(row_frame, text=text, command=command,
                                   font=(FONT_FAMILY, 14, 'bold'),
                                   bg=color, fg='white',
                                   width=20, height=3,
                                   relief=tk.RAISED, bd=2)
                    btn.pack(side=tk.LEFT, padx=10, expand=True, fill=tk.X)
                else:
                    # إضافة مساحة فارغة
                    spacer = tk.Frame(row_frame, bg=COLORS['background'])
                    spacer.pack(side=tk.LEFT, padx=10, expand=True, fill=tk.X)
                
    def create_quick_info(self, parent):
        """إنشاء معلومات سريعة"""
        info_label = tk.Label(parent, text="معلومات سريعة",
                             font=(FONT_FAMILY, 16, 'bold'),
                             bg=COLORS['background'])
        info_label.pack(anchor=tk.W)
        
        # إطار للمعلومات
        stats_frame = tk.Frame(parent, bg=COLORS['background'])
        stats_frame.pack(fill=tk.X, pady=5)
        
        # عرض إحصائيات سريعة
        try:
            products_count = len(self.db.get_all_products())
            customers_count = len(self.db.get_all_customers())
            suppliers_count = len(self.db.get_all_suppliers())
            low_stock_count = len(self.db.get_low_stock_products())

            # إحصائيات المبيعات اليومية
            from datetime import datetime
            today = datetime.now().strftime('%Y-%m-%d')
            daily_summary = self.db.get_daily_sales_summary(today)

            # إحصائيات الديون
            customers_with_debts = self.db.get_customers_with_debts()
            total_debt = sum(c['current_debt'] for c in customers_with_debts)

            # الصف الأول من الإحصائيات
            stats_text1 = f"المنتجات: {products_count} | الزبائن: {customers_count} | الموردين: {suppliers_count} | منخفض المخزون: {low_stock_count}"

            stats_label1 = tk.Label(stats_frame, text=stats_text1,
                                   font=(FONT_FAMILY, 11),
                                   bg=COLORS['background'])
            stats_label1.pack(anchor=tk.W)

            # الصف الثاني من الإحصائيات
            stats_text2 = f"مبيعات اليوم: {daily_summary['sales_count']} | المبلغ: {daily_summary['total_sales']:.2f} {CURRENCY_SYMBOL} | الديون: {total_debt:.2f} {CURRENCY_SYMBOL}"

            stats_label2 = tk.Label(stats_frame, text=stats_text2,
                                   font=(FONT_FAMILY, 11),
                                   bg=COLORS['background'])
            stats_label2.pack(anchor=tk.W)

        except Exception as e:
            error_label = tk.Label(stats_frame, text="خطأ في تحميل البيانات",
                                  font=(FONT_FAMILY, 12),
                                  bg=COLORS['background'], fg='red')
            error_label.pack(anchor=tk.W)

    # ==================== دوال الأحداث ====================

    def open_new_sale(self):
        """فتح نافذة بيع جديد"""
        try:
            from gui.sales import NewSaleWindow
            NewSaleWindow(self.root, self.db)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة البيع: {str(e)}")

    def view_sales(self):
        """عرض المبيعات"""
        try:
            from gui.sales import SalesWindow
            sales_window = SalesWindow(self.root)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة المبيعات: {str(e)}")

    def manage_products(self):
        """إدارة المنتجات"""
        try:
            from gui.inventory import InventoryWindow
            inventory_window = InventoryWindow(self.root)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة إدارة المنتجات: {str(e)}")

    def inventory_report(self):
        """تقرير المخزون"""
        try:
            products = self.db.get_all_products()
            low_stock_products = self.db.get_low_stock_products()

            total_products = len(products)
            total_value = sum(p['purchase_price'] * p['stock_quantity'] for p in products)
            low_stock_count = len(low_stock_products)

            report_text = f"تقرير المخزون\n\n"
            report_text += f"إجمالي المنتجات: {total_products}\n"
            report_text += f"القيمة الإجمالية للمخزون: {total_value:.2f} {CURRENCY_SYMBOL}\n"
            report_text += f"منتجات منخفضة المخزون: {low_stock_count}\n\n"

            if low_stock_products:
                report_text += "المنتجات منخفضة المخزون:\n"
                for product in low_stock_products[:10]:
                    report_text += f"- {product['name']}: {product['stock_quantity']} {product['unit']}\n"

                if len(low_stock_products) > 10:
                    report_text += f"... و {len(low_stock_products) - 10} منتجات أخرى"

            messagebox.showinfo("تقرير المخزون", report_text)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير المخزون: {str(e)}")

    def manage_customers(self):
        """إدارة الزبائن"""
        try:
            from gui.customers import CustomersWindow
            customers_window = CustomersWindow(self.root)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة إدارة الزبائن: {str(e)}")

    def debts_report(self):
        """تقرير الديون"""
        try:
            customers_with_debts = self.db.get_customers_with_debts()
            if not customers_with_debts:
                messagebox.showinfo("تقرير الديون", "لا يوجد زبائن لديهم ديون")
                return

            total_debt = sum(customer['current_debt'] for customer in customers_with_debts)

            report_text = f"تقرير الديون\n\n"
            report_text += f"عدد الزبائن المدينين: {len(customers_with_debts)}\n"
            report_text += f"إجمالي الديون: {total_debt:.2f} {CURRENCY_SYMBOL}\n\n"
            report_text += "تفاصيل الديون:\n"

            for customer in customers_with_debts[:10]:  # أول 10 زبائن
                report_text += f"- {customer['name']}: {customer['current_debt']:.2f} {CURRENCY_SYMBOL}\n"

            if len(customers_with_debts) > 10:
                report_text += f"... و {len(customers_with_debts) - 10} زبائن آخرين"

            messagebox.showinfo("تقرير الديون", report_text)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير الديون: {str(e)}")

    def manage_suppliers(self):
        """إدارة الموردين"""
        try:
            from gui.suppliers import SuppliersWindow
            suppliers_window = SuppliersWindow(self.root)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة إدارة الموردين: {str(e)}")

    def manage_purchases(self):
        """إدارة المشتريات"""
        try:
            from gui.purchases import PurchasesWindow
            purchases_window = PurchasesWindow(self.root, self.db)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة إدارة المشتريات: {str(e)}")

    def daily_sales_report(self):
        """تقرير المبيعات اليومية"""
        try:
            from datetime import datetime
            today = datetime.now().strftime('%Y-%m-%d')
            summary = self.db.get_daily_sales_summary(today)

            report_text = f"تقرير المبيعات اليومية - {today}\n\n"
            report_text += f"عدد المبيعات: {summary['sales_count']}\n"
            report_text += f"إجمالي المبيعات: {summary['total_sales']:.2f} {CURRENCY_SYMBOL}\n"
            report_text += f"المبلغ المدفوع: {summary['total_paid']:.2f} {CURRENCY_SYMBOL}\n"
            report_text += f"المبلغ المتبقي: {summary['total_remaining']:.2f} {CURRENCY_SYMBOL}\n"

            messagebox.showinfo("التقرير اليومي", report_text)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير اليومي: {str(e)}")

    def monthly_sales_report(self):
        """تقرير المبيعات الشهرية"""
        try:
            from datetime import datetime
            now = datetime.now()
            summary = self.db.get_monthly_sales_report(now.year, now.month)

            report_text = f"تقرير المبيعات الشهرية - {now.year}/{now.month:02d}\n\n"
            report_text += f"عدد المبيعات: {summary['sales_count']}\n"
            report_text += f"إجمالي المبيعات: {summary['total_sales']:.2f} {CURRENCY_SYMBOL}\n"
            report_text += f"المبلغ المدفوع: {summary['total_paid']:.2f} {CURRENCY_SYMBOL}\n"
            report_text += f"المبلغ المتبقي: {summary['total_remaining']:.2f} {CURRENCY_SYMBOL}\n"
            report_text += f"متوسط البيع: {summary['average_sale']:.2f} {CURRENCY_SYMBOL}\n"

            messagebox.showinfo("التقرير الشهري", report_text)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير الشهري: {str(e)}")

    def profit_report(self):
        """تقرير الأرباح"""
        try:
            profit_data = self.db.get_profit_report()

            report_text = f"تقرير الأرباح\n\n"
            report_text += f"إجمالي الإيرادات: {profit_data['total_revenue']:.2f} {CURRENCY_SYMBOL}\n"
            report_text += f"إجمالي التكلفة: {profit_data['total_cost']:.2f} {CURRENCY_SYMBOL}\n"
            report_text += f"إجمالي الربح: {profit_data['total_profit']:.2f} {CURRENCY_SYMBOL}\n"
            report_text += f"هامش الربح: {profit_data['profit_margin']:.1f}%\n"

            messagebox.showinfo("تقرير الأرباح", report_text)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير الأرباح: {str(e)}")

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            from utils.backup import BackupManager
            backup_manager = BackupManager()

            backup_path = backup_manager.create_backup()
            backup_size = backup_manager.get_backup_size(backup_path)

            messagebox.showinfo("نجح",
                               f"تم إنشاء النسخة الاحتياطية بنجاح\n\n"
                               f"المسار: {backup_path}\n"
                               f"الحجم: {backup_size}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        try:
            from utils.backup import BackupManager
            from tkinter import filedialog

            backup_manager = BackupManager()

            # اختيار ملف النسخة الاحتياطية
            backup_file = filedialog.askopenfilename(
                title="اختر ملف النسخة الاحتياطية",
                filetypes=[("Database files", "*.db"), ("All files", "*.*")],
                initialdir=backup_manager.backup_folder
            )

            if backup_file:
                # تأكيد الاستعادة
                if messagebox.askyesno("تأكيد الاستعادة",
                                     "هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\n\n"
                                     "تحذير: سيتم استبدال البيانات الحالية"):
                    backup_manager.restore_backup(backup_file)
                    messagebox.showinfo("نجح", "تم استعادة النسخة الاحتياطية بنجاح")

                    # إعادة تحميل البيانات
                    self.refresh_quick_info()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في استعادة النسخة الاحتياطية: {str(e)}")

    def show_settings(self):
        """عرض الإعدادات"""
        try:
            from gui.settings_window import SettingsWindow
            SettingsWindow(self.root, self.db)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة الإعدادات: {str(e)}")

    def open_settings(self):
        """فتح نافذة الإعدادات"""
        self.show_settings()

    def refresh_quick_info(self):
        """تحديث المعلومات السريعة"""
        try:
            # البحث عن إطار المعلومات السريعة وتحديثه
            for widget in self.root.winfo_children():
                if isinstance(widget, tk.Frame):
                    for child in widget.winfo_children():
                        if isinstance(child, tk.Label) and "معلومات سريعة" in child.cget("text"):
                            # إعادة إنشاء المعلومات السريعة
                            parent = child.master
                            for w in parent.winfo_children():
                                if w != child:
                                    w.destroy()
                            self.create_quick_info(parent)
                            return
        except Exception as e:
            print(f"خطأ في تحديث المعلومات السريعة: {e}")

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        about_text = f"""
{WINDOW_TITLE}

برنامج محاسبة شامل للصيدلية الزراعية
يتضمن إدارة المبيعات والمخزون والزبائن والموردين

الإصدار: 1.0
تطوير: فريق التطوير
        """
        messagebox.showinfo("حول البرنامج", about_text)

    def run(self):
        """تشغيل البرنامج"""
        self.root.mainloop()

if __name__ == "__main__":
    # التأكد من وجود المجلدات المطلوبة
    ensure_folders_exist()

    # تشغيل البرنامج
    app = MainWindow()
    app.run()
