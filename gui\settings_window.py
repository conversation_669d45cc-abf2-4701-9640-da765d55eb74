# -*- coding: utf-8 -*-
"""
نافذة الإعدادات المتقدمة لبرنامج محاسبة الصيدلية الزراعية
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, colorchooser
import sys
import os
import json
from datetime import datetime

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import *

class SettingsWindow:
    """نافذة الإعدادات المتقدمة"""
    
    def __init__(self, parent, db):
        self.parent = parent
        self.db = db
        
        self.window = tk.Toplevel(parent)
        self.window.title("إعدادات البرنامج")
        self.window.geometry("800x600")
        self.window.configure(bg=COLORS['background'])
        self.window.transient(parent)
        self.window.grab_set()
        
        # تحميل الإعدادات الحالية
        self.load_current_settings()
        
        self.create_interface()
        self.center_window()
        
    def center_window(self):
        """وضع النافذة في المنتصف"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def load_current_settings(self):
        """تحميل الإعدادات الحالية"""
        self.settings = {
            'company_name': COMPANY_NAME,
            'company_address': COMPANY_ADDRESS,
            'company_phone': COMPANY_PHONE,
            'company_email': COMPANY_EMAIL,
            'currency_symbol': CURRENCY_SYMBOL,
            'backup_interval': 7,  # أيام
            'auto_backup': True,
            'low_stock_threshold': 10,
            'theme': 'default',
            'font_size': 12,
            'show_notifications': True,
            'backup_location': 'backups',
            'max_backup_files': 10
        }
        
        # محاولة تحميل الإعدادات من ملف
        try:
            if os.path.exists('config/user_settings.json'):
                with open('config/user_settings.json', 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    self.settings.update(saved_settings)
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
    
    def create_interface(self):
        """إنشاء واجهة الإعدادات"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg=COLORS['primary'], height=60)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="إعدادات البرنامج",
                              font=(FONT_FAMILY, 18, 'bold'),
                              bg=COLORS['primary'], fg='white')
        title_label.pack(expand=True)
        
        # إطار التبويبات
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # تبويب معلومات الشركة
        company_frame = tk.Frame(notebook, bg=COLORS['background'])
        notebook.add(company_frame, text="معلومات الشركة")
        self.create_company_tab(company_frame)
        
        # تبويب النسخ الاحتياطية
        backup_frame = tk.Frame(notebook, bg=COLORS['background'])
        notebook.add(backup_frame, text="النسخ الاحتياطية")
        self.create_backup_tab(backup_frame)
        
        # تبويب المظهر
        appearance_frame = tk.Frame(notebook, bg=COLORS['background'])
        notebook.add(appearance_frame, text="المظهر والواجهة")
        self.create_appearance_tab(appearance_frame)
        
        # تبويب التنبيهات
        notifications_frame = tk.Frame(notebook, bg=COLORS['background'])
        notebook.add(notifications_frame, text="التنبيهات")
        self.create_notifications_tab(notifications_frame)
        
        # تبويب متقدم
        advanced_frame = tk.Frame(notebook, bg=COLORS['background'])
        notebook.add(advanced_frame, text="إعدادات متقدمة")
        self.create_advanced_tab(advanced_frame)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Button(buttons_frame, text="حفظ الإعدادات",
                 command=self.save_settings,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['success'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="استعادة الافتراضي",
                 command=self.reset_to_default,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['warning'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تصدير الإعدادات",
                 command=self.export_settings,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['info'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="استيراد الإعدادات",
                 command=self.import_settings,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['secondary'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="إلغاء",
                 command=self.window.destroy,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['danger'], fg='white',
                 width=15).pack(side=tk.RIGHT, padx=5)
    
    def create_company_tab(self, parent):
        """إنشاء تبويب معلومات الشركة"""
        # إطار المعلومات
        info_frame = tk.LabelFrame(parent, text="معلومات الشركة",
                                  font=(FONT_FAMILY, 14, 'bold'),
                                  bg=COLORS['background'])
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # اسم الشركة
        tk.Label(info_frame, text="اسم الشركة:",
                font=(FONT_FAMILY, 12, 'bold'),
                bg=COLORS['background']).grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        
        self.company_name_var = tk.StringVar(value=self.settings['company_name'])
        tk.Entry(info_frame, textvariable=self.company_name_var,
                font=(FONT_FAMILY, 12), width=40).grid(row=0, column=1, padx=10, pady=5)
        
        # عنوان الشركة
        tk.Label(info_frame, text="العنوان:",
                font=(FONT_FAMILY, 12, 'bold'),
                bg=COLORS['background']).grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        
        self.company_address_var = tk.StringVar(value=self.settings['company_address'])
        tk.Entry(info_frame, textvariable=self.company_address_var,
                font=(FONT_FAMILY, 12), width=40).grid(row=1, column=1, padx=10, pady=5)
        
        # هاتف الشركة
        tk.Label(info_frame, text="الهاتف:",
                font=(FONT_FAMILY, 12, 'bold'),
                bg=COLORS['background']).grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        
        self.company_phone_var = tk.StringVar(value=self.settings['company_phone'])
        tk.Entry(info_frame, textvariable=self.company_phone_var,
                font=(FONT_FAMILY, 12), width=40).grid(row=2, column=1, padx=10, pady=5)
        
        # بريد الشركة
        tk.Label(info_frame, text="البريد الإلكتروني:",
                font=(FONT_FAMILY, 12, 'bold'),
                bg=COLORS['background']).grid(row=3, column=0, sticky=tk.W, padx=10, pady=5)
        
        self.company_email_var = tk.StringVar(value=self.settings['company_email'])
        tk.Entry(info_frame, textvariable=self.company_email_var,
                font=(FONT_FAMILY, 12), width=40).grid(row=3, column=1, padx=10, pady=5)
        
        # رمز العملة
        tk.Label(info_frame, text="رمز العملة:",
                font=(FONT_FAMILY, 12, 'bold'),
                bg=COLORS['background']).grid(row=4, column=0, sticky=tk.W, padx=10, pady=5)
        
        self.currency_var = tk.StringVar(value=self.settings['currency_symbol'])
        currency_combo = ttk.Combobox(info_frame, textvariable=self.currency_var,
                                     values=['ريال', 'دولار', 'يورو', 'دينار', 'درهم'],
                                     font=(FONT_FAMILY, 12), width=37)
        currency_combo.grid(row=4, column=1, padx=10, pady=5)
    
    def create_backup_tab(self, parent):
        """إنشاء تبويب النسخ الاحتياطية"""
        # إطار النسخ الاحتياطية
        backup_frame = tk.LabelFrame(parent, text="إعدادات النسخ الاحتياطية",
                                    font=(FONT_FAMILY, 14, 'bold'),
                                    bg=COLORS['background'])
        backup_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # تفعيل النسخ التلقائي
        self.auto_backup_var = tk.BooleanVar(value=self.settings['auto_backup'])
        tk.Checkbutton(backup_frame, text="تفعيل النسخ الاحتياطي التلقائي",
                      variable=self.auto_backup_var,
                      font=(FONT_FAMILY, 12),
                      bg=COLORS['background']).grid(row=0, column=0, columnspan=2, sticky=tk.W, padx=10, pady=5)
        
        # فترة النسخ الاحتياطي
        tk.Label(backup_frame, text="فترة النسخ الاحتياطي (أيام):",
                font=(FONT_FAMILY, 12, 'bold'),
                bg=COLORS['background']).grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        
        self.backup_interval_var = tk.IntVar(value=self.settings['backup_interval'])
        tk.Spinbox(backup_frame, from_=1, to=30, textvariable=self.backup_interval_var,
                  font=(FONT_FAMILY, 12), width=10).grid(row=1, column=1, sticky=tk.W, padx=10, pady=5)
        
        # مجلد النسخ الاحتياطية
        tk.Label(backup_frame, text="مجلد النسخ الاحتياطية:",
                font=(FONT_FAMILY, 12, 'bold'),
                bg=COLORS['background']).grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        
        backup_location_frame = tk.Frame(backup_frame, bg=COLORS['background'])
        backup_location_frame.grid(row=2, column=1, sticky=tk.W, padx=10, pady=5)
        
        self.backup_location_var = tk.StringVar(value=self.settings['backup_location'])
        tk.Entry(backup_location_frame, textvariable=self.backup_location_var,
                font=(FONT_FAMILY, 12), width=30).pack(side=tk.LEFT)
        
        tk.Button(backup_location_frame, text="تصفح",
                 command=self.browse_backup_location,
                 font=(FONT_FAMILY, 10)).pack(side=tk.LEFT, padx=5)
        
        # عدد النسخ الاحتياطية المحفوظة
        tk.Label(backup_frame, text="عدد النسخ المحفوظة:",
                font=(FONT_FAMILY, 12, 'bold'),
                bg=COLORS['background']).grid(row=3, column=0, sticky=tk.W, padx=10, pady=5)
        
        self.max_backup_var = tk.IntVar(value=self.settings['max_backup_files'])
        tk.Spinbox(backup_frame, from_=5, to=50, textvariable=self.max_backup_var,
                  font=(FONT_FAMILY, 12), width=10).grid(row=3, column=1, sticky=tk.W, padx=10, pady=5)
        
        # أزرار النسخ الاحتياطي
        backup_buttons_frame = tk.Frame(backup_frame, bg=COLORS['background'])
        backup_buttons_frame.grid(row=4, column=0, columnspan=2, pady=10)
        
        tk.Button(backup_buttons_frame, text="إنشاء نسخة احتياطية الآن",
                 command=self.create_backup_now,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['success'], fg='white').pack(side=tk.LEFT, padx=5)
        
        tk.Button(backup_buttons_frame, text="استعادة نسخة احتياطية",
                 command=self.restore_backup,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['warning'], fg='white').pack(side=tk.LEFT, padx=5)
    
    def create_appearance_tab(self, parent):
        """إنشاء تبويب المظهر"""
        # إطار المظهر
        appearance_frame = tk.LabelFrame(parent, text="إعدادات المظهر",
                                        font=(FONT_FAMILY, 14, 'bold'),
                                        bg=COLORS['background'])
        appearance_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # حجم الخط
        tk.Label(appearance_frame, text="حجم الخط:",
                font=(FONT_FAMILY, 12, 'bold'),
                bg=COLORS['background']).grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        
        self.font_size_var = tk.IntVar(value=self.settings['font_size'])
        tk.Spinbox(appearance_frame, from_=8, to=20, textvariable=self.font_size_var,
                  font=(FONT_FAMILY, 12), width=10).grid(row=0, column=1, sticky=tk.W, padx=10, pady=5)
        
        # السمة
        tk.Label(appearance_frame, text="السمة:",
                font=(FONT_FAMILY, 12, 'bold'),
                bg=COLORS['background']).grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        
        self.theme_var = tk.StringVar(value=self.settings['theme'])
        theme_combo = ttk.Combobox(appearance_frame, textvariable=self.theme_var,
                                  values=['default', 'dark', 'light', 'blue'],
                                  font=(FONT_FAMILY, 12), width=20)
        theme_combo.grid(row=1, column=1, sticky=tk.W, padx=10, pady=5)
    
    def create_notifications_tab(self, parent):
        """إنشاء تبويب التنبيهات"""
        # إطار التنبيهات
        notifications_frame = tk.LabelFrame(parent, text="إعدادات التنبيهات",
                                           font=(FONT_FAMILY, 14, 'bold'),
                                           bg=COLORS['background'])
        notifications_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # تفعيل التنبيهات
        self.show_notifications_var = tk.BooleanVar(value=self.settings['show_notifications'])
        tk.Checkbutton(notifications_frame, text="تفعيل التنبيهات",
                      variable=self.show_notifications_var,
                      font=(FONT_FAMILY, 12),
                      bg=COLORS['background']).grid(row=0, column=0, columnspan=2, sticky=tk.W, padx=10, pady=5)
        
        # حد المخزون المنخفض
        tk.Label(notifications_frame, text="حد تنبيه المخزون المنخفض:",
                font=(FONT_FAMILY, 12, 'bold'),
                bg=COLORS['background']).grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        
        self.low_stock_var = tk.IntVar(value=self.settings['low_stock_threshold'])
        tk.Spinbox(notifications_frame, from_=1, to=100, textvariable=self.low_stock_var,
                  font=(FONT_FAMILY, 12), width=10).grid(row=1, column=1, sticky=tk.W, padx=10, pady=5)
    
    def create_advanced_tab(self, parent):
        """إنشاء تبويب الإعدادات المتقدمة"""
        # إطار الإعدادات المتقدمة
        advanced_frame = tk.LabelFrame(parent, text="إعدادات متقدمة",
                                      font=(FONT_FAMILY, 14, 'bold'),
                                      bg=COLORS['background'])
        advanced_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # معلومات قاعدة البيانات
        tk.Label(advanced_frame, text="معلومات قاعدة البيانات:",
                font=(FONT_FAMILY, 12, 'bold'),
                bg=COLORS['background']).grid(row=0, column=0, columnspan=2, sticky=tk.W, padx=10, pady=5)
        
        # حجم قاعدة البيانات
        try:
            db_size = os.path.getsize('pharmacy_accounting.db') / (1024 * 1024)  # MB
            tk.Label(advanced_frame, text=f"حجم قاعدة البيانات: {db_size:.2f} ميجابايت",
                    font=(FONT_FAMILY, 10),
                    bg=COLORS['background']).grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=20, pady=2)
        except:
            pass
        
        # عدد السجلات
        try:
            conn = self.db.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM products")
            products_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM customers")
            customers_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM sales")
            sales_count = cursor.fetchone()[0]
            
            conn.close()
            
            tk.Label(advanced_frame, text=f"عدد المنتجات: {products_count}",
                    font=(FONT_FAMILY, 10),
                    bg=COLORS['background']).grid(row=2, column=0, sticky=tk.W, padx=20, pady=2)
            
            tk.Label(advanced_frame, text=f"عدد الزبائن: {customers_count}",
                    font=(FONT_FAMILY, 10),
                    bg=COLORS['background']).grid(row=2, column=1, sticky=tk.W, padx=20, pady=2)
            
            tk.Label(advanced_frame, text=f"عدد المبيعات: {sales_count}",
                    font=(FONT_FAMILY, 10),
                    bg=COLORS['background']).grid(row=3, column=0, sticky=tk.W, padx=20, pady=2)
        except:
            pass
        
        # أزرار الصيانة
        maintenance_frame = tk.Frame(advanced_frame, bg=COLORS['background'])
        maintenance_frame.grid(row=4, column=0, columnspan=2, pady=10)
        
        tk.Button(maintenance_frame, text="تحسين قاعدة البيانات",
                 command=self.optimize_database,
                 font=(FONT_FAMILY, 10),
                 bg=COLORS['info'], fg='white').pack(side=tk.LEFT, padx=5)
        
        tk.Button(maintenance_frame, text="فحص سلامة البيانات",
                 command=self.check_data_integrity,
                 font=(FONT_FAMILY, 10),
                 bg=COLORS['warning'], fg='white').pack(side=tk.LEFT, padx=5)

    def browse_backup_location(self):
        """تصفح مجلد النسخ الاحتياطية"""
        folder = filedialog.askdirectory(title="اختر مجلد النسخ الاحتياطية")
        if folder:
            self.backup_location_var.set(folder)

    def create_backup_now(self):
        """إنشاء نسخة احتياطية فورية"""
        try:
            from utils.backup import BackupManager
            backup_manager = BackupManager(self.db)
            backup_file = backup_manager.create_backup()
            messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{backup_file}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        backup_file = filedialog.askopenfilename(
            title="اختر ملف النسخة الاحتياطية",
            filetypes=[("Database files", "*.db"), ("All files", "*.*")]
        )

        if backup_file:
            result = messagebox.askyesno(
                "تأكيد الاستعادة",
                "هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\nسيتم استبدال البيانات الحالية."
            )

            if result:
                try:
                    from utils.backup import BackupManager
                    backup_manager = BackupManager(self.db)
                    backup_manager.restore_backup(backup_file)
                    messagebox.showinfo("نجح", "تم استعادة النسخة الاحتياطية بنجاح")
                    messagebox.showinfo("إعادة تشغيل", "يرجى إعادة تشغيل البرنامج لتطبيق التغييرات")
                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في استعادة النسخة الاحتياطية: {str(e)}")

    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        try:
            conn = self.db.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute("VACUUM")
            conn.close()
            messagebox.showinfo("نجح", "تم تحسين قاعدة البيانات بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحسين قاعدة البيانات: {str(e)}")

    def check_data_integrity(self):
        """فحص سلامة البيانات"""
        try:
            conn = self.db.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute("PRAGMA integrity_check")
            result = cursor.fetchone()[0]
            conn.close()

            if result == "ok":
                messagebox.showinfo("فحص سلامة البيانات", "قاعدة البيانات سليمة ولا توجد أخطاء")
            else:
                messagebox.showwarning("فحص سلامة البيانات", f"تم العثور على مشاكل: {result}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فحص سلامة البيانات: {str(e)}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # تحديث الإعدادات
            self.settings.update({
                'company_name': self.company_name_var.get(),
                'company_address': self.company_address_var.get(),
                'company_phone': self.company_phone_var.get(),
                'company_email': self.company_email_var.get(),
                'currency_symbol': self.currency_var.get(),
                'backup_interval': self.backup_interval_var.get(),
                'auto_backup': self.auto_backup_var.get(),
                'low_stock_threshold': self.low_stock_var.get(),
                'theme': self.theme_var.get(),
                'font_size': self.font_size_var.get(),
                'show_notifications': self.show_notifications_var.get(),
                'backup_location': self.backup_location_var.get(),
                'max_backup_files': self.max_backup_var.get()
            })

            # إنشاء مجلد config إذا لم يكن موجوداً
            os.makedirs('config', exist_ok=True)

            # حفظ الإعدادات في ملف
            with open('config/user_settings.json', 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح\nسيتم تطبيق بعض الإعدادات عند إعادة تشغيل البرنامج")
            self.window.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الإعدادات: {str(e)}")

    def reset_to_default(self):
        """استعادة الإعدادات الافتراضية"""
        result = messagebox.askyesno(
            "تأكيد الاستعادة",
            "هل أنت متأكد من استعادة الإعدادات الافتراضية؟\nسيتم فقدان جميع الإعدادات المخصصة."
        )

        if result:
            try:
                # حذف ملف الإعدادات المخصصة
                if os.path.exists('config/user_settings.json'):
                    os.remove('config/user_settings.json')

                # إعادة تحميل الإعدادات الافتراضية
                self.load_current_settings()

                # تحديث الواجهة
                self.update_interface_values()

                messagebox.showinfo("نجح", "تم استعادة الإعدادات الافتراضية بنجاح")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في استعادة الإعدادات الافتراضية: {str(e)}")

    def update_interface_values(self):
        """تحديث قيم الواجهة"""
        self.company_name_var.set(self.settings['company_name'])
        self.company_address_var.set(self.settings['company_address'])
        self.company_phone_var.set(self.settings['company_phone'])
        self.company_email_var.set(self.settings['company_email'])
        self.currency_var.set(self.settings['currency_symbol'])
        self.backup_interval_var.set(self.settings['backup_interval'])
        self.auto_backup_var.set(self.settings['auto_backup'])
        self.low_stock_var.set(self.settings['low_stock_threshold'])
        self.theme_var.set(self.settings['theme'])
        self.font_size_var.set(self.settings['font_size'])
        self.show_notifications_var.set(self.settings['show_notifications'])
        self.backup_location_var.set(self.settings['backup_location'])
        self.max_backup_var.set(self.settings['max_backup_files'])

    def export_settings(self):
        """تصدير الإعدادات"""
        filename = filedialog.asksaveasfilename(
            title="تصدير الإعدادات",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.settings, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("نجح", f"تم تصدير الإعدادات بنجاح إلى:\n{filename}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تصدير الإعدادات: {str(e)}")

    def import_settings(self):
        """استيراد الإعدادات"""
        filename = filedialog.askopenfilename(
            title="استيراد الإعدادات",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    imported_settings = json.load(f)

                # تحديث الإعدادات
                self.settings.update(imported_settings)

                # تحديث الواجهة
                self.update_interface_values()

                messagebox.showinfo("نجح", "تم استيراد الإعدادات بنجاح")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في استيراد الإعدادات: {str(e)}")

if __name__ == "__main__":
    # اختبار النافذة
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية

    # محاكاة قاعدة البيانات
    class MockDB:
        class db_manager:
            @staticmethod
            def get_connection():
                import sqlite3
                return sqlite3.connect(':memory:')

    app = SettingsWindow(root, MockDB())
    root.mainloop()
