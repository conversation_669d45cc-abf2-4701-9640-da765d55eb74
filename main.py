# -*- coding: utf-8 -*-
"""
الملف الرئيسي لبرنامج محاسبة الصيدلية الزراعية
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from gui.main_window import MainWindow
    from config.settings import ensure_folders_exist
    from database.database import PharmacyDatabase
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

def main():
    """الدالة الرئيسية لتشغيل البرنامج"""
    try:
        # التأكد من وجود المجلدات المطلوبة
        ensure_folders_exist()
        
        # اختبار الاتصال بقاعدة البيانات
        db = PharmacyDatabase()
        
        # تشغيل الواجهة الرئيسية
        app = MainWindow()
        app.run()
        
    except Exception as e:
        # عرض رسالة خطأ في حالة فشل تشغيل البرنامج
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        messagebox.showerror("خطأ في تشغيل البرنامج", 
                           f"حدث خطأ أثناء تشغيل البرنامج:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
