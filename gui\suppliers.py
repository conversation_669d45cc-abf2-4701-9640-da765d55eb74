# -*- coding: utf-8 -*-
"""
واجهة إدارة الموردين لبرنامج محاسبة الصيدلية الزراعية
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import *
from database.database import PharmacyDatabase

class SuppliersWindow:
    """نافذة إدارة الموردين"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.db = PharmacyDatabase()
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.setup_window()
        self.create_interface()
        self.load_suppliers()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة الموردين")
        self.window.geometry("1000x600")
        self.window.configure(bg=COLORS['background'])
        self.center_window()
        
    def center_window(self):
        """وضع النافذة في منتصف الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg=COLORS['primary'], height=60)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="إدارة الموردين",
                              font=(FONT_FAMILY, 18, 'bold'),
                              bg=COLORS['primary'], fg='white')
        title_label.pack(expand=True)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # أزرار العمليات
        tk.Button(buttons_frame, text="إضافة مورد جديد",
                 command=self.add_supplier,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['success'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تعديل مورد",
                 command=self.edit_supplier,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['primary'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="حذف مورد",
                 command=self.delete_supplier,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['warning'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تفاصيل المورد",
                 command=self.view_supplier_details,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['secondary'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="إدارة منتجات المورد",
                 command=self.manage_supplier_products,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['info'], fg='white',
                 width=18).pack(side=tk.LEFT, padx=5)
        
        # إطار البحث
        search_frame = tk.Frame(self.window, bg=COLORS['background'])
        search_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(search_frame, text="البحث:",
                font=(FONT_FAMILY, 12),
                bg=COLORS['background']).pack(side=tk.LEFT, padx=5)
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.filter_suppliers)
        search_entry = tk.Entry(search_frame, textvariable=self.search_var,
                               font=(FONT_FAMILY, 12), width=30)
        search_entry.pack(side=tk.LEFT, padx=5)
        
        # إطار الجدول
        table_frame = tk.Frame(self.window, bg=COLORS['background'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # إنشاء الجدول
        self.create_suppliers_table(table_frame)
        
        # إطار المعلومات
        info_frame = tk.Frame(self.window, bg=COLORS['background'])
        info_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.info_label = tk.Label(info_frame, text="",
                                  font=(FONT_FAMILY, 10),
                                  bg=COLORS['background'])
        self.info_label.pack(anchor=tk.W)
        
    def create_suppliers_table(self, parent):
        """إنشاء جدول الموردين"""
        columns = ('id', 'name', 'phone', 'address', 'email', 'products_count')
        
        self.tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة
        headers = {
            'id': 'الرقم',
            'name': 'اسم المورد',
            'phone': 'الهاتف',
            'address': 'العنوان',
            'email': 'البريد الإلكتروني',
            'products_count': 'عدد المنتجات'
        }
        
        for col in columns:
            self.tree.heading(col, text=headers[col])
            if col == 'id':
                self.tree.column(col, width=60, anchor=tk.CENTER)
            elif col == 'products_count':
                self.tree.column(col, width=100, anchor=tk.CENTER)
            elif col == 'phone':
                self.tree.column(col, width=120, anchor=tk.CENTER)
            else:
                self.tree.column(col, width=180, anchor=tk.CENTER)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.tree.bind('<Double-1>', self.on_item_double_click)
        
    def load_suppliers(self):
        """تحميل الموردين في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل الموردين من قاعدة البيانات
            suppliers = self.db.get_all_suppliers()
            
            for supplier in suppliers:
                # حساب عدد المنتجات لكل مورد
                products_count = self.get_supplier_products_count(supplier['id'])
                
                self.tree.insert('', tk.END, values=(
                    supplier['id'],
                    supplier['name'],
                    supplier['phone'] or '',
                    supplier['address'] or '',
                    supplier['email'] or '',
                    products_count
                ))
            
            # تحديث معلومات الإحصائيات
            self.update_info_label(len(suppliers))
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الموردين: {str(e)}")
    
    def get_supplier_products_count(self, supplier_id):
        """حساب عدد المنتجات لمورد معين"""
        try:
            conn = self.db.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM products WHERE supplier_id = ?', (supplier_id,))
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except:
            return 0
    
    def update_info_label(self, total_suppliers):
        """تحديث تسمية المعلومات"""
        info_text = f"إجمالي الموردين: {total_suppliers}"
        self.info_label.config(text=info_text)
    
    def filter_suppliers(self, *args):
        """تصفية الموردين حسب النص المدخل"""
        search_text = self.search_var.get().lower()
        
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        try:
            # تحميل جميع الموردين
            suppliers = self.db.get_all_suppliers()
            
            # تصفية الموردين
            filtered_suppliers = []
            for supplier in suppliers:
                if (search_text in supplier['name'].lower() or 
                    search_text in (supplier['phone'] or '').lower() or
                    search_text in (supplier['address'] or '').lower()):
                    filtered_suppliers.append(supplier)
            
            # عرض الموردين المصفاة
            for supplier in filtered_suppliers:
                products_count = self.get_supplier_products_count(supplier['id'])
                
                self.tree.insert('', tk.END, values=(
                    supplier['id'],
                    supplier['name'],
                    supplier['phone'] or '',
                    supplier['address'] or '',
                    supplier['email'] or '',
                    products_count
                ))
            
            # تحديث المعلومات
            self.update_info_label(len(filtered_suppliers))
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصفية الموردين: {str(e)}")
    
    def add_supplier(self):
        """إضافة مورد جديد"""
        dialog = SupplierDialog(self.window, "إضافة مورد جديد")
        if dialog.result:
            try:
                supplier_data = dialog.result
                self.db.add_supplier(
                    name=supplier_data['name'],
                    phone=supplier_data['phone'],
                    address=supplier_data['address'],
                    email=supplier_data['email']
                )
                messagebox.showinfo("نجح", "تم إضافة المورد بنجاح")
                self.load_suppliers()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة المورد: {str(e)}")
    
    def edit_supplier(self):
        """تعديل مورد محدد"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد للتعديل")
            return
        
        # الحصول على بيانات المورد المحدد
        item_values = self.tree.item(selected_item[0])['values']
        supplier_id = item_values[0]
        
        # الحصول على البيانات الكاملة من قاعدة البيانات
        supplier = self.db.get_supplier_by_id(supplier_id)
        if not supplier:
            messagebox.showerror("خطأ", "لم يتم العثور على المورد")
            return
        
        # فتح نافذة التعديل
        dialog = SupplierDialog(self.window, "تعديل مورد", supplier)
        if dialog.result:
            try:
                supplier_data = dialog.result
                self.db.update_supplier(
                    supplier_id=supplier_id,
                    name=supplier_data['name'],
                    phone=supplier_data['phone'],
                    address=supplier_data['address'],
                    email=supplier_data['email']
                )
                messagebox.showinfo("نجح", "تم تعديل المورد بنجاح")
                self.load_suppliers()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تعديل المورد: {str(e)}")
    
    def delete_supplier(self):
        """حذف مورد محدد"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد للحذف")
            return
        
        # الحصول على بيانات المورد
        item_values = self.tree.item(selected_item[0])['values']
        supplier_id = item_values[0]
        supplier_name = item_values[1]
        products_count = item_values[5]
        
        if products_count > 0:
            messagebox.showwarning("تحذير", 
                                 f"لا يمكن حذف المورد '{supplier_name}' لأنه مرتبط بـ {products_count} منتج.\n"
                                 "يرجى حذف أو تعديل المنتجات المرتبطة أولاً.")
            return
        
        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف", 
                              f"هل أنت متأكد من حذف المورد '{supplier_name}'؟\n\nتحذير: لا يمكن التراجع عن هذا الإجراء"):
            try:
                self.db.delete_supplier(supplier_id)
                messagebox.showinfo("نجح", "تم حذف المورد بنجاح")
                self.load_suppliers()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المورد: {str(e)}")
    
    def view_supplier_details(self):
        """عرض تفاصيل المورد الشاملة"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد لعرض تفاصيله")
            return

        # الحصول على بيانات المورد
        item_values = self.tree.item(selected_item[0])['values']
        supplier_id = item_values[0]
        supplier_name = item_values[1]

        try:
            # فتح نافذة تفاصيل المورد الشاملة
            from gui.supplier_details import SupplierDetailsWindow
            SupplierDetailsWindow(self.window, supplier_id, supplier_name, self.db)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل تفاصيل المورد: {str(e)}")

    def manage_supplier_products(self):
        """إدارة منتجات المورد"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد لإدارة منتجاته")
            return

        # الحصول على بيانات المورد
        item_values = self.tree.item(selected_item[0])['values']
        supplier_id = item_values[0]
        supplier_name = item_values[1]

        try:
            # فتح نافذة إدارة منتجات المورد
            SupplierProductsManagerWindow(self.window, supplier_id, supplier_name, self.db, self.load_suppliers)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة إدارة المنتجات: {str(e)}")

    def view_supplier_products(self):
        """عرض منتجات مورد معين"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد لعرض منتجاته")
            return

        # الحصول على بيانات المورد
        item_values = self.tree.item(selected_item[0])['values']
        supplier_id = item_values[0]
        supplier_name = item_values[1]

        try:
            # الحصول على منتجات المورد
            conn = self.db.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, name, category, unit, purchase_price, selling_price, stock_quantity
                FROM products
                WHERE supplier_id = ?
                ORDER BY name
            ''', (supplier_id,))

            products = [dict(row) for row in cursor.fetchall()]
            conn.close()

            if not products:
                messagebox.showinfo("معلومة", f"لا توجد منتجات للمورد '{supplier_name}'")
                return

            # إنشاء نافذة عرض المنتجات
            SupplierProductsWindow(self.window, supplier_name, products)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل منتجات المورد: {str(e)}")
    
    def on_item_double_click(self, event):
        """التعامل مع النقر المزدوج على عنصر"""
        self.edit_supplier()

class SupplierDialog:
    """نافذة حوار إضافة/تعديل مورد"""

    def __init__(self, parent, title="إضافة مورد", supplier_data=None):
        self.result = None
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("450x350")
        self.dialog.configure(bg=COLORS['background'])
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.supplier_data = supplier_data
        self.create_form()
        self.center_dialog()

    def center_dialog(self):
        """وضع النافذة في المنتصف"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f'{width}x{height}+{x}+{y}')

    def create_form(self):
        """إنشاء نموذج إدخال البيانات"""
        # إطار النموذج
        form_frame = tk.Frame(self.dialog, bg=COLORS['background'])
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # حقول الإدخال
        self.entries = {}

        fields = [
            ('name', 'اسم المورد', 'str', True),
            ('phone', 'رقم الهاتف', 'str', False),
            ('address', 'العنوان', 'str', False),
            ('email', 'البريد الإلكتروني', 'str', False)
        ]

        for i, (field, label, field_type, required) in enumerate(fields):
            # تسمية الحقل
            label_text = label + ("*" if required else "") + ":"
            tk.Label(form_frame, text=label_text,
                    font=(FONT_FAMILY, 12),
                    bg=COLORS['background']).grid(row=i, column=0, sticky=tk.W, pady=8)

            # حقل الإدخال
            entry = tk.Entry(form_frame, font=(FONT_FAMILY, 12), width=30)
            entry.grid(row=i, column=1, sticky=tk.W, pady=8, padx=10)
            self.entries[field] = entry

            # تعبئة البيانات الموجودة (في حالة التعديل)
            if self.supplier_data and field in self.supplier_data:
                value = self.supplier_data[field]
                if value is not None:
                    entry.insert(0, str(value))

        # ملاحظة الحقول المطلوبة
        note_label = tk.Label(form_frame, text="* حقول مطلوبة",
                             font=(FONT_FAMILY, 10),
                             bg=COLORS['background'], fg='red')
        note_label.grid(row=len(fields), column=0, columnspan=2, sticky=tk.W, pady=5)

        # أزرار العمليات
        buttons_frame = tk.Frame(form_frame, bg=COLORS['background'])
        buttons_frame.grid(row=len(fields)+1, column=0, columnspan=2, pady=20)

        tk.Button(buttons_frame, text="حفظ",
                 command=self.save_supplier,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['success'], fg='white',
                 width=12).pack(side=tk.LEFT, padx=10)

        tk.Button(buttons_frame, text="إلغاء",
                 command=self.dialog.destroy,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['warning'], fg='white',
                 width=12).pack(side=tk.LEFT, padx=10)

    def save_supplier(self):
        """حفظ بيانات المورد"""
        try:
            # التحقق من صحة البيانات
            data = {}

            # اسم المورد (مطلوب)
            name = self.entries['name'].get().strip()
            if not name:
                raise ValueError("يرجى إدخال اسم المورد")
            data['name'] = name

            # رقم الهاتف
            phone = self.entries['phone'].get().strip()
            data['phone'] = phone if phone else None

            # العنوان
            address = self.entries['address'].get().strip()
            data['address'] = address if address else None

            # البريد الإلكتروني
            email = self.entries['email'].get().strip()
            if email and '@' not in email:
                raise ValueError("يرجى إدخال بريد إلكتروني صحيح")
            data['email'] = email if email else None

            # حفظ البيانات
            self.result = data
            self.dialog.destroy()

        except ValueError as e:
            messagebox.showerror("خطأ في البيانات", str(e))

class SupplierProductsWindow:
    """نافذة عرض منتجات المورد"""

    def __init__(self, parent, supplier_name, products):
        self.window = tk.Toplevel(parent)
        self.window.title(f"منتجات المورد: {supplier_name}")
        self.window.geometry("900x500")
        self.window.configure(bg=COLORS['background'])
        self.window.transient(parent)

        self.products = products
        self.create_interface()
        self.load_products()
        self.center_window()

    def center_window(self):
        """وضع النافذة في المنتصف"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def create_interface(self):
        """إنشاء واجهة عرض المنتجات"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg=COLORS['primary'], height=50)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="منتجات المورد",
                              font=(FONT_FAMILY, 16, 'bold'),
                              bg=COLORS['primary'], fg='white')
        title_label.pack(expand=True)

        # إطار الجدول
        table_frame = tk.Frame(self.window, bg=COLORS['background'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # إنشاء جدول المنتجات
        columns = ('id', 'name', 'category', 'unit', 'purchase_price', 'selling_price', 'stock_quantity')

        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # تعيين عناوين الأعمدة
        headers = {
            'id': 'الرقم',
            'name': 'اسم المنتج',
            'category': 'الفئة',
            'unit': 'الوحدة',
            'purchase_price': 'سعر الشراء',
            'selling_price': 'سعر البيع',
            'stock_quantity': 'الكمية'
        }

        for col in columns:
            self.tree.heading(col, text=headers[col])
            if col == 'id':
                self.tree.column(col, width=60, anchor=tk.CENTER)
            elif col in ['purchase_price', 'selling_price', 'stock_quantity']:
                self.tree.column(col, width=100, anchor=tk.CENTER)
            elif col in ['unit']:
                self.tree.column(col, width=80, anchor=tk.CENTER)
            else:
                self.tree.column(col, width=150, anchor=tk.CENTER)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # إطار الملخص
        summary_frame = tk.Frame(self.window, bg=COLORS['background'])
        summary_frame.pack(fill=tk.X, padx=10, pady=5)

        self.summary_label = tk.Label(summary_frame, text="",
                                     font=(FONT_FAMILY, 12, 'bold'),
                                     bg=COLORS['background'])
        self.summary_label.pack()

        # زر الإغلاق
        close_button = tk.Button(self.window, text="إغلاق",
                               command=self.window.destroy,
                               font=(FONT_FAMILY, 12, 'bold'),
                               bg=COLORS['warning'], fg='white',
                               width=15)
        close_button.pack(pady=10)

    def load_products(self):
        """تحميل المنتجات في الجدول"""
        total_value = 0
        total_quantity = 0

        for product in self.products:
            # تلوين المنتجات منخفضة المخزون
            tags = []
            if product['stock_quantity'] <= 5:  # افتراض الحد الأدنى 5
                tags.append('low_stock')
            elif product['stock_quantity'] == 0:
                tags.append('out_of_stock')

            self.tree.insert('', tk.END, values=(
                product['id'],
                product['name'],
                product['category'],
                product['unit'],
                f"{product['purchase_price']:.2f}",
                f"{product['selling_price']:.2f}",
                product['stock_quantity']
            ), tags=tags)

            # حساب القيم الإجمالية
            total_value += product['purchase_price'] * product['stock_quantity']
            total_quantity += product['stock_quantity']

        # تعيين ألوان للصفوف
        self.tree.tag_configure('low_stock', background='#ffcccc')
        self.tree.tag_configure('out_of_stock', background='#ff9999')

        # تحديث الملخص
        self.summary_label.config(
            text=f"إجمالي المنتجات: {len(self.products)} | إجمالي الكمية: {total_quantity} | القيمة الإجمالية: {total_value:.2f} {CURRENCY_SYMBOL}"
        )

class SupplierProductsManagerWindow:
    """نافذة إدارة منتجات المورد"""

    def __init__(self, parent, supplier_id, supplier_name, db, refresh_callback=None):
        self.parent = parent
        self.supplier_id = supplier_id
        self.supplier_name = supplier_name
        self.db = db
        self.refresh_callback = refresh_callback

        self.window = tk.Toplevel(parent)
        self.window.title(f"إدارة منتجات المورد: {supplier_name}")
        self.window.geometry("1200x700")
        self.window.configure(bg=COLORS['background'])
        self.window.transient(parent)
        self.window.grab_set()

        self.create_interface()
        self.load_products()
        self.center_window()

    def center_window(self):
        """وضع النافذة في المنتصف"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def create_interface(self):
        """إنشاء واجهة إدارة منتجات المورد"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg=COLORS['info'], height=60)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text=f"إدارة منتجات المورد: {self.supplier_name}",
                              font=(FONT_FAMILY, 16, 'bold'),
                              bg=COLORS['info'], fg='white')
        title_label.pack(expand=True)

        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Button(buttons_frame, text="➕ إضافة منتج جديد",
                 command=self.add_product,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['success'], fg='white',
                 width=18).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="✏️ تعديل منتج",
                 command=self.edit_product,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['primary'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="🗑️ حذف منتج",
                 command=self.delete_product,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['danger'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="🔄 تحديث",
                 command=self.load_products,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['secondary'], fg='white',
                 width=12).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="❌ إغلاق",
                 command=self.window.destroy,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['warning'], fg='white',
                 width=12).pack(side=tk.RIGHT, padx=5)

        # إطار البحث
        search_frame = tk.Frame(self.window, bg=COLORS['background'])
        search_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(search_frame, text="البحث:",
                font=(FONT_FAMILY, 12, 'bold'),
                bg=COLORS['background']).pack(side=tk.LEFT, padx=5)

        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.filter_products)
        search_entry = tk.Entry(search_frame, textvariable=self.search_var,
                               font=(FONT_FAMILY, 12), width=30)
        search_entry.pack(side=tk.LEFT, padx=5)

        # إطار الجدول
        table_frame = tk.Frame(self.window, bg=COLORS['background'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # إنشاء جدول المنتجات
        columns = ('id', 'name', 'category', 'unit', 'purchase_price', 'selling_price', 'stock_quantity', 'min_stock')

        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # تعيين عناوين الأعمدة
        headers = {
            'id': 'الرقم',
            'name': 'اسم المنتج',
            'category': 'الفئة',
            'unit': 'الوحدة',
            'purchase_price': 'سعر الشراء',
            'selling_price': 'سعر البيع',
            'stock_quantity': 'الكمية',
            'min_stock': 'الحد الأدنى'
        }

        for col in columns:
            self.tree.heading(col, text=headers[col])
            if col == 'id':
                self.tree.column(col, width=60, anchor=tk.CENTER)
            elif col == 'name':
                self.tree.column(col, width=200, anchor=tk.W)
            elif col in ['purchase_price', 'selling_price']:
                self.tree.column(col, width=100, anchor=tk.CENTER)
            else:
                self.tree.column(col, width=120, anchor=tk.CENTER)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # إطار الملخص
        summary_frame = tk.Frame(self.window, bg=COLORS['background'])
        summary_frame.pack(fill=tk.X, padx=10, pady=5)

        self.summary_label = tk.Label(summary_frame, text="",
                                     font=(FONT_FAMILY, 12, 'bold'),
                                     bg=COLORS['background'])
        self.summary_label.pack()

        # ربط النقر المزدوج
        self.tree.bind('<Double-1>', lambda e: self.edit_product())

    def load_products(self):
        """تحميل منتجات المورد"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            # تحميل منتجات المورد من قاعدة البيانات
            conn = self.db.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, name, category, unit, purchase_price, selling_price, stock_quantity, min_stock
                FROM products
                WHERE supplier_id = ?
                ORDER BY name
            ''', (self.supplier_id,))

            products = cursor.fetchall()
            conn.close()

            total_products = 0
            total_value = 0
            low_stock_count = 0

            for product in products:
                # تلوين المنتجات منخفضة المخزون
                tags = []
                if product[6] <= product[7]:  # stock_quantity <= min_stock
                    tags.append('low_stock')
                    low_stock_count += 1

                self.tree.insert('', tk.END, values=(
                    product[0],  # id
                    product[1],  # name
                    product[2],  # category
                    product[3],  # unit
                    f"{product[4]:.2f}",  # purchase_price
                    f"{product[5]:.2f}",  # selling_price
                    product[6],  # stock_quantity
                    product[7]   # min_stock
                ), tags=tags)

                total_products += 1
                total_value += product[4] * product[6]  # purchase_price * stock_quantity

            # تعيين ألوان للصفوف
            self.tree.tag_configure('low_stock', background='#ffcccc')

            # تحديث الملخص
            self.summary_label.config(
                text=f"إجمالي المنتجات: {total_products} | القيمة الإجمالية: {total_value:.2f} {CURRENCY_SYMBOL} | منخفض المخزون: {low_stock_count}"
            )

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل منتجات المورد: {str(e)}")

    def filter_products(self, *args):
        """تصفية المنتجات حسب البحث"""
        search_term = self.search_var.get().lower()

        # إخفاء جميع العناصر
        for item in self.tree.get_children():
            self.tree.detach(item)

        # إظهار العناصر المطابقة فقط
        for item in self.tree.get_children():
            values = self.tree.item(item)['values']
            if any(search_term in str(value).lower() for value in values):
                self.tree.reattach(item, '', 'end')

    def add_product(self):
        """إضافة منتج جديد للمورد"""
        try:
            dialog = ProductDialog(self.window, "إضافة منتج جديد", supplier_id=self.supplier_id)
            if dialog.result:
                product_data = dialog.result

                # إضافة المنتج مع ربطه بالمورد
                product_id = self.db.add_product(
                    name=product_data['name'],
                    category=product_data['category'],
                    unit=product_data['unit'],
                    purchase_price=product_data['purchase_price'],
                    selling_price=product_data['selling_price'],
                    stock_quantity=product_data['stock_quantity'],
                    min_stock=product_data['min_stock'],
                    supplier_id=self.supplier_id
                )

                messagebox.showinfo("نجح", f"تم إضافة المنتج '{product_data['name']}' بنجاح")
                self.load_products()

                # تحديث عدد المنتجات في نافذة الموردين
                if self.refresh_callback:
                    self.refresh_callback()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إضافة المنتج: {str(e)}")

    def edit_product(self):
        """تعديل منتج محدد"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للتعديل")
            return

        # الحصول على بيانات المنتج المحدد
        item_values = self.tree.item(selected_item[0])['values']
        product_id = item_values[0]

        # الحصول على البيانات الكاملة من قاعدة البيانات
        product = self.db.get_product_by_id(product_id)
        if not product:
            messagebox.showerror("خطأ", "لم يتم العثور على المنتج")
            return

        try:
            dialog = ProductDialog(self.window, "تعديل منتج", product_data=product, supplier_id=self.supplier_id)
            if dialog.result:
                product_data = dialog.result

                self.db.update_product(
                    product_id=product_id,
                    name=product_data['name'],
                    category=product_data['category'],
                    unit=product_data['unit'],
                    purchase_price=product_data['purchase_price'],
                    selling_price=product_data['selling_price'],
                    stock_quantity=product_data['stock_quantity'],
                    min_stock=product_data['min_stock']
                )

                messagebox.showinfo("نجح", f"تم تعديل المنتج '{product_data['name']}' بنجاح")
                self.load_products()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تعديل المنتج: {str(e)}")

    def delete_product(self):
        """حذف منتج محدد"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للحذف")
            return

        # الحصول على بيانات المنتج
        item_values = self.tree.item(selected_item[0])['values']
        product_id = item_values[0]
        product_name = item_values[1]
        stock_quantity = int(item_values[6])

        # تحذير إذا كان هناك مخزون
        if stock_quantity > 0:
            response = messagebox.askyesno(
                "تحذير - يوجد مخزون",
                f"المنتج '{product_name}' يحتوي على {stock_quantity} وحدة في المخزون.\n\n"
                f"هل أنت متأكد من حذفه؟\n"
                f"سيتم فقدان جميع البيانات المرتبطة به."
            )
            if not response:
                return

        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف",
                              f"هل أنت متأكد من حذف المنتج '{product_name}'؟\n\nتحذير: لا يمكن التراجع عن هذا الإجراء"):
            try:
                self.db.delete_product(product_id)
                messagebox.showinfo("نجح", f"تم حذف المنتج '{product_name}' بنجاح")
                self.load_products()

                # تحديث عدد المنتجات في نافذة الموردين
                if self.refresh_callback:
                    self.refresh_callback()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المنتج: {str(e)}")

class ProductDialog:
    """نافذة حوار إضافة/تعديل منتج"""

    def __init__(self, parent, title="إضافة منتج", product_data=None, supplier_id=None):
        self.result = None
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x600")
        self.dialog.configure(bg=COLORS['background'])
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.product_data = product_data
        self.supplier_id = supplier_id
        self.create_form()
        self.center_dialog()

    def center_dialog(self):
        """وضع النافذة في المنتصف"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f'{width}x{height}+{x}+{y}')

    def create_form(self):
        """إنشاء نموذج إدخال البيانات"""
        # إطار النموذج
        form_frame = tk.Frame(self.dialog, bg=COLORS['background'])
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # حقول الإدخال
        self.entries = {}

        fields = [
            ('name', 'اسم المنتج', 'str', True),
            ('category', 'الفئة', 'str', True),
            ('unit', 'الوحدة', 'str', True),
            ('purchase_price', 'سعر الشراء', 'float', True),
            ('selling_price', 'سعر البيع', 'float', True),
            ('stock_quantity', 'الكمية الحالية', 'int', True),
            ('min_stock', 'الحد الأدنى للمخزون', 'int', True)
        ]

        for i, (field, label, field_type, required) in enumerate(fields):
            # تسمية الحقل
            label_text = label + ("*" if required else "") + ":"
            tk.Label(form_frame, text=label_text,
                    font=(FONT_FAMILY, 12),
                    bg=COLORS['background']).grid(row=i, column=0, sticky=tk.W, pady=8)

            # حقل الإدخال
            if field == 'category':
                # قائمة منسدلة للفئات
                entry = ttk.Combobox(form_frame, font=(FONT_FAMILY, 12), width=28)
                entry['values'] = ['أدوية', 'مبيدات', 'أسمدة', 'بذور', 'أدوات زراعية', 'أخرى']
                entry['state'] = 'readonly'
            elif field == 'unit':
                # قائمة منسدلة للوحدات
                entry = ttk.Combobox(form_frame, font=(FONT_FAMILY, 12), width=28)
                entry['values'] = ['كيلو', 'جرام', 'لتر', 'مليلتر', 'قطعة', 'علبة', 'كيس', 'زجاجة']
                entry['state'] = 'readonly'
            else:
                entry = tk.Entry(form_frame, font=(FONT_FAMILY, 12), width=30)

            entry.grid(row=i, column=1, sticky=tk.W, pady=8, padx=10)
            self.entries[field] = entry

            # تعبئة البيانات الموجودة (في حالة التعديل)
            if self.product_data and field in self.product_data:
                value = self.product_data[field]
                if value is not None:
                    if field in ['category', 'unit']:
                        entry.set(str(value))
                    else:
                        entry.insert(0, str(value))

        # ملاحظة الحقول المطلوبة
        note_label = tk.Label(form_frame, text="* حقول مطلوبة",
                             font=(FONT_FAMILY, 10),
                             bg=COLORS['background'], fg='red')
        note_label.grid(row=len(fields), column=0, columnspan=2, sticky=tk.W, pady=5)

        # أزرار العمليات
        buttons_frame = tk.Frame(form_frame, bg=COLORS['background'])
        buttons_frame.grid(row=len(fields)+1, column=0, columnspan=2, pady=20)

        tk.Button(buttons_frame, text="حفظ",
                 command=self.save_product,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['success'], fg='white',
                 width=12).pack(side=tk.LEFT, padx=10)

        tk.Button(buttons_frame, text="إلغاء",
                 command=self.dialog.destroy,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['warning'], fg='white',
                 width=12).pack(side=tk.LEFT, padx=10)

    def save_product(self):
        """حفظ بيانات المنتج"""
        try:
            # التحقق من صحة البيانات
            data = {}

            # اسم المنتج (مطلوب)
            name = self.entries['name'].get().strip()
            if not name:
                raise ValueError("يرجى إدخال اسم المنتج")
            data['name'] = name

            # الفئة (مطلوبة)
            category = self.entries['category'].get().strip()
            if not category:
                raise ValueError("يرجى اختيار فئة المنتج")
            data['category'] = category

            # الوحدة (مطلوبة)
            unit = self.entries['unit'].get().strip()
            if not unit:
                raise ValueError("يرجى اختيار وحدة المنتج")
            data['unit'] = unit

            # سعر الشراء (مطلوب)
            try:
                purchase_price = float(self.entries['purchase_price'].get())
                if purchase_price < 0:
                    raise ValueError("سعر الشراء يجب أن يكون أكبر من أو يساوي صفر")
                data['purchase_price'] = purchase_price
            except ValueError:
                raise ValueError("يرجى إدخال سعر شراء صحيح")

            # سعر البيع (مطلوب)
            try:
                selling_price = float(self.entries['selling_price'].get())
                if selling_price < 0:
                    raise ValueError("سعر البيع يجب أن يكون أكبر من أو يساوي صفر")
                data['selling_price'] = selling_price
            except ValueError:
                raise ValueError("يرجى إدخال سعر بيع صحيح")

            # الكمية الحالية (مطلوبة)
            try:
                stock_quantity = int(self.entries['stock_quantity'].get())
                if stock_quantity < 0:
                    raise ValueError("الكمية يجب أن تكون أكبر من أو تساوي صفر")
                data['stock_quantity'] = stock_quantity
            except ValueError:
                raise ValueError("يرجى إدخال كمية صحيحة")

            # الحد الأدنى للمخزون (مطلوب)
            try:
                min_stock = int(self.entries['min_stock'].get())
                if min_stock < 0:
                    raise ValueError("الحد الأدنى يجب أن يكون أكبر من أو يساوي صفر")
                data['min_stock'] = min_stock
            except ValueError:
                raise ValueError("يرجى إدخال حد أدنى صحيح")

            # التحقق من منطقية الأسعار
            if selling_price < purchase_price:
                response = messagebox.askyesno(
                    "تحذير",
                    f"سعر البيع ({selling_price:.2f}) أقل من سعر الشراء ({purchase_price:.2f})\n"
                    f"هذا قد يؤدي إلى خسارة. هل تريد المتابعة؟"
                )
                if not response:
                    return

            self.result = data
            self.dialog.destroy()

        except ValueError as e:
            messagebox.showerror("خطأ في البيانات", str(e))
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ المنتج: {str(e)}")

if __name__ == "__main__":
    # اختبار النافذة
    app = SuppliersWindow()
    app.window.mainloop()
