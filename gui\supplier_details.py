# -*- coding: utf-8 -*-
"""
نافذة تفاصيل المورد الشاملة لبرنامج محاسبة الصيدلية الزراعية
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import *

class SupplierDetailsWindow:
    """نافذة تفاصيل المورد الشاملة"""
    
    def __init__(self, parent, supplier_id, supplier_name, db):
        self.parent = parent
        self.supplier_id = supplier_id
        self.supplier_name = supplier_name
        self.db = db
        
        self.window = tk.Toplevel(parent)
        self.window.title(f"تفاصيل المورد: {supplier_name}")
        self.window.geometry("1200x800")
        self.window.configure(bg=COLORS['background'])
        self.window.transient(parent)
        
        self.create_interface()
        self.load_supplier_data()
        self.center_window()
        
    def center_window(self):
        """وضع النافذة في المنتصف"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_interface(self):
        """إنشاء واجهة تفاصيل المورد"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg=COLORS['primary'], height=60)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text=f"ملف المورد: {self.supplier_name}",
                              font=(FONT_FAMILY, 18, 'bold'),
                              bg=COLORS['primary'], fg='white')
        title_label.pack(expand=True)
        
        # إطار المعلومات الأساسية
        info_frame = tk.LabelFrame(self.window, text="المعلومات الأساسية",
                                  font=(FONT_FAMILY, 14, 'bold'),
                                  bg=COLORS['background'], fg=COLORS['text'])
        info_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.create_basic_info(info_frame)
        
        # إطار الإحصائيات
        stats_frame = tk.LabelFrame(self.window, text="الإحصائيات",
                                   font=(FONT_FAMILY, 14, 'bold'),
                                   bg=COLORS['background'], fg=COLORS['text'])
        stats_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.create_statistics(stats_frame)
        
        # إطار التبويبات
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # تبويب المنتجات
        products_frame = tk.Frame(notebook, bg=COLORS['background'])
        notebook.add(products_frame, text="المنتجات")
        self.create_products_tab(products_frame)
        
        # تبويب المشتريات
        purchases_frame = tk.Frame(notebook, bg=COLORS['background'])
        notebook.add(purchases_frame, text="المشتريات")
        self.create_purchases_tab(purchases_frame)
        
        # تبويب المدفوعات
        payments_frame = tk.Frame(notebook, bg=COLORS['background'])
        notebook.add(payments_frame, text="المدفوعات للمورد")
        self.create_payments_tab(payments_frame)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Button(buttons_frame, text="إضافة مشتريات",
                 command=self.add_purchase,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['success'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="إضافة دفعة",
                 command=self.add_payment,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['info'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="طباعة كشف حساب",
                 command=self.print_statement,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['secondary'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تحديث البيانات",
                 command=self.refresh_data,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['primary'], fg='white',
                 width=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="إغلاق",
                 command=self.window.destroy,
                 font=(FONT_FAMILY, 12, 'bold'),
                 bg=COLORS['warning'], fg='white',
                 width=15).pack(side=tk.RIGHT, padx=5)
    
    def create_basic_info(self, parent):
        """إنشاء قسم المعلومات الأساسية"""
        self.info_labels = {}
        
        info_data = [
            ("الاسم:", "name"),
            ("الهاتف:", "phone"),
            ("العنوان:", "address"),
            ("البريد الإلكتروني:", "email"),
            ("تاريخ التسجيل:", "created_at"),
            ("آخر تحديث:", "updated_at")
        ]
        
        for i, (label_text, key) in enumerate(info_data):
            row = i // 3
            col = (i % 3) * 2
            
            tk.Label(parent, text=label_text,
                    font=(FONT_FAMILY, 12, 'bold'),
                    bg=COLORS['background']).grid(row=row, column=col, sticky=tk.W, padx=10, pady=5)
            
            value_label = tk.Label(parent, text="",
                                  font=(FONT_FAMILY, 12),
                                  bg=COLORS['background'])
            value_label.grid(row=row, column=col+1, sticky=tk.W, padx=10, pady=5)
            self.info_labels[key] = value_label
    
    def create_statistics(self, parent):
        """إنشاء قسم الإحصائيات"""
        self.stats_labels = {}
        
        stats_data = [
            ("عدد المنتجات:", "products_count"),
            ("إجمالي المشتريات:", "total_purchases"),
            ("إجمالي المدفوعات:", "total_payments"),
            ("آخر مشترى:", "last_purchase"),
            ("آخر دفعة:", "last_payment"),
            ("الرصيد المستحق:", "outstanding_balance")
        ]
        
        for i, (label_text, key) in enumerate(stats_data):
            row = i // 3
            col = (i % 3) * 2
            
            tk.Label(parent, text=label_text,
                    font=(FONT_FAMILY, 12, 'bold'),
                    bg=COLORS['background']).grid(row=row, column=col, sticky=tk.W, padx=10, pady=5)
            
            value_label = tk.Label(parent, text="",
                                  font=(FONT_FAMILY, 12),
                                  bg=COLORS['background'])
            value_label.grid(row=row, column=col+1, sticky=tk.W, padx=10, pady=5)
            self.stats_labels[key] = value_label
    
    def create_products_tab(self, parent):
        """إنشاء تبويب المنتجات"""
        columns = ('product_id', 'name', 'category', 'unit', 'purchase_price', 'selling_price', 'stock_quantity')
        
        self.products_tree = ttk.Treeview(parent, columns=columns, show='headings', height=12)
        
        headers = {
            'product_id': 'رقم المنتج',
            'name': 'اسم المنتج',
            'category': 'الفئة',
            'unit': 'الوحدة',
            'purchase_price': 'سعر الشراء',
            'selling_price': 'سعر البيع',
            'stock_quantity': 'الكمية'
        }
        
        for col in columns:
            self.products_tree.heading(col, text=headers[col])
            if col == 'name':
                self.products_tree.column(col, width=200, anchor=tk.W)
            elif col in ['category', 'unit']:
                self.products_tree.column(col, width=100, anchor=tk.CENTER)
            else:
                self.products_tree.column(col, width=120, anchor=tk.CENTER)
        
        # شريط التمرير للمنتجات
        products_scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=products_scrollbar.set)
        
        self.products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        products_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_purchases_tab(self, parent):
        """إنشاء تبويب المشتريات"""
        columns = ('purchase_id', 'purchase_date', 'total_amount', 'paid_amount', 'remaining_amount', 'notes')
        
        self.purchases_tree = ttk.Treeview(parent, columns=columns, show='headings', height=12)
        
        headers = {
            'purchase_id': 'رقم المشترى',
            'purchase_date': 'التاريخ',
            'total_amount': 'المبلغ الإجمالي',
            'paid_amount': 'المدفوع',
            'remaining_amount': 'المتبقي',
            'notes': 'ملاحظات'
        }
        
        for col in columns:
            self.purchases_tree.heading(col, text=headers[col])
            if col == 'notes':
                self.purchases_tree.column(col, width=200, anchor=tk.W)
            else:
                self.purchases_tree.column(col, width=120, anchor=tk.CENTER)
        
        # شريط التمرير للمشتريات
        purchases_scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.purchases_tree.yview)
        self.purchases_tree.configure(yscrollcommand=purchases_scrollbar.set)
        
        self.purchases_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        purchases_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط النقر المزدوج لعرض تفاصيل المشترى
        self.purchases_tree.bind('<Double-1>', self.view_purchase_details)
    
    def create_payments_tab(self, parent):
        """إنشاء تبويب المدفوعات"""
        columns = ('payment_id', 'payment_date', 'amount', 'payment_type', 'payment_method', 'notes')
        
        self.payments_tree = ttk.Treeview(parent, columns=columns, show='headings', height=12)
        
        headers = {
            'payment_id': 'رقم الدفعة',
            'payment_date': 'التاريخ',
            'amount': 'المبلغ',
            'payment_type': 'النوع',
            'payment_method': 'طريقة الدفع',
            'notes': 'ملاحظات'
        }
        
        for col in columns:
            self.payments_tree.heading(col, text=headers[col])
            if col == 'notes':
                self.payments_tree.column(col, width=200, anchor=tk.W)
            else:
                self.payments_tree.column(col, width=120, anchor=tk.CENTER)
        
        # شريط التمرير للمدفوعات
        payments_scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.payments_tree.yview)
        self.payments_tree.configure(yscrollcommand=payments_scrollbar.set)
        
        self.payments_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        payments_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def load_supplier_data(self):
        """تحميل بيانات المورد"""
        try:
            # تحميل المعلومات الأساسية
            supplier = self.db.get_supplier_by_id(self.supplier_id)
            if supplier:
                self.info_labels['name'].config(text=supplier['name'])
                self.info_labels['phone'].config(text=supplier['phone'] or 'غير محدد')
                self.info_labels['address'].config(text=supplier['address'] or 'غير محدد')
                self.info_labels['email'].config(text=supplier['email'] or 'غير محدد')
                self.info_labels['created_at'].config(text=supplier['created_at'])
                self.info_labels['updated_at'].config(text=supplier['updated_at'])

            # تحميل الإحصائيات
            self.load_statistics()

            # تحميل المنتجات
            self.load_products()

            # تحميل المشتريات
            self.load_purchases()

            # تحميل المدفوعات
            self.load_payments()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات المورد: {str(e)}")

    def load_statistics(self):
        """تحميل الإحصائيات"""
        try:
            conn = self.db.db_manager.get_connection()
            cursor = conn.cursor()

            # عدد المنتجات
            cursor.execute('SELECT COUNT(*) FROM products WHERE supplier_id = ?', (self.supplier_id,))
            products_count = cursor.fetchone()[0]

            # إحصائيات المشتريات
            cursor.execute('''
                SELECT COUNT(*) as purchases_count,
                       COALESCE(SUM(total_amount), 0) as total_purchases,
                       MAX(purchase_date) as last_purchase
                FROM purchases
                WHERE supplier_id = ?
            ''', (self.supplier_id,))

            purchase_stats = dict(cursor.fetchone())

            # إحصائيات المدفوعات
            cursor.execute('''
                SELECT COALESCE(SUM(amount), 0) as total_payments,
                       MAX(payment_date) as last_payment
                FROM payments
                WHERE supplier_id = ? AND payment_type = 'دفع'
            ''', (self.supplier_id,))

            payment_stats = dict(cursor.fetchone())

            conn.close()

            # تحديث التسميات
            self.stats_labels['products_count'].config(text=str(products_count))
            self.stats_labels['total_purchases'].config(text=f"{purchase_stats['total_purchases']:.2f} {CURRENCY_SYMBOL}")
            self.stats_labels['total_payments'].config(text=f"{payment_stats['total_payments']:.2f} {CURRENCY_SYMBOL}")
            self.stats_labels['last_purchase'].config(text=purchase_stats['last_purchase'] or 'لا يوجد')
            self.stats_labels['last_payment'].config(text=payment_stats['last_payment'] or 'لا يوجد')

            # الرصيد المستحق
            outstanding_balance = purchase_stats['total_purchases'] - payment_stats['total_payments']
            self.stats_labels['outstanding_balance'].config(text=f"{outstanding_balance:.2f} {CURRENCY_SYMBOL}")

        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {str(e)}")

    def load_products(self):
        """تحميل المنتجات"""
        try:
            # مسح البيانات الحالية
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)

            # تحميل منتجات المورد
            conn = self.db.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, name, category, unit, purchase_price, selling_price, stock_quantity
                FROM products
                WHERE supplier_id = ?
                ORDER BY name
            ''', (self.supplier_id,))

            products = cursor.fetchall()
            conn.close()

            for product in products:
                # تلوين المنتجات منخفضة المخزون
                tags = []
                if product[6] <= 5:  # stock_quantity
                    tags.append('low_stock')
                elif product[6] == 0:
                    tags.append('out_of_stock')

                self.products_tree.insert('', tk.END, values=(
                    product[0],  # id
                    product[1],  # name
                    product[2],  # category
                    product[3],  # unit
                    f"{product[4]:.2f}",  # purchase_price
                    f"{product[5]:.2f}",  # selling_price
                    product[6]   # stock_quantity
                ), tags=tags)

            # تعيين ألوان للصفوف
            self.products_tree.tag_configure('low_stock', background='#ffcccc')
            self.products_tree.tag_configure('out_of_stock', background='#ff9999')

        except Exception as e:
            print(f"خطأ في تحميل المنتجات: {str(e)}")

    def load_purchases(self):
        """تحميل المشتريات"""
        try:
            # مسح البيانات الحالية
            for item in self.purchases_tree.get_children():
                self.purchases_tree.delete(item)

            # تحميل مشتريات المورد
            conn = self.db.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, purchase_date, total_amount, paid_amount, remaining_amount, notes
                FROM purchases
                WHERE supplier_id = ?
                ORDER BY purchase_date DESC
            ''', (self.supplier_id,))

            purchases = cursor.fetchall()
            conn.close()

            for purchase in purchases:
                # تلوين الصفوف حسب حالة الدفع
                tags = []
                if purchase[4] > 0:  # remaining_amount
                    tags.append('has_debt')

                self.purchases_tree.insert('', tk.END, values=(
                    purchase[0],  # id
                    purchase[1],  # purchase_date
                    f"{purchase[2]:.2f}",  # total_amount
                    f"{purchase[3]:.2f}",  # paid_amount
                    f"{purchase[4]:.2f}",  # remaining_amount
                    purchase[5] or ''  # notes
                ), tags=tags)

            # تعيين ألوان للصفوف
            self.purchases_tree.tag_configure('has_debt', background='#fff3cd')

        except Exception as e:
            print(f"خطأ في تحميل المشتريات: {str(e)}")

    def load_payments(self):
        """تحميل المدفوعات"""
        try:
            # مسح البيانات الحالية
            for item in self.payments_tree.get_children():
                self.payments_tree.delete(item)

            # تحميل مدفوعات المورد
            conn = self.db.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, payment_date, amount, payment_type, payment_method, notes
                FROM payments
                WHERE supplier_id = ?
                ORDER BY payment_date DESC
            ''', (self.supplier_id,))

            payments = cursor.fetchall()
            conn.close()

            for payment in payments:
                self.payments_tree.insert('', tk.END, values=(
                    payment[0],  # id
                    payment[1],  # payment_date
                    f"{payment[2]:.2f}",  # amount
                    payment[3],  # payment_type
                    payment[4],  # payment_method
                    payment[5] or ''  # notes
                ))

        except Exception as e:
            print(f"خطأ في تحميل المدفوعات: {str(e)}")

    def view_purchase_details(self, event):
        """عرض تفاصيل المشترى المحدد"""
        selected_item = self.purchases_tree.selection()
        if not selected_item:
            return

        # الحصول على رقم المشترى
        item_values = self.purchases_tree.item(selected_item[0])['values']
        purchase_id = item_values[0]

        messagebox.showinfo("تفاصيل المشترى", f"سيتم تطوير تفاصيل المشترى رقم {purchase_id} قريباً")

    def add_purchase(self):
        """إضافة مشتريات جديدة"""
        messagebox.showinfo("قريباً", "سيتم تطوير إضافة المشتريات قريباً")

    def add_payment(self):
        """إضافة دفعة للمورد"""
        # طلب مبلغ الدفعة
        amount = simpledialog.askfloat(
            "إضافة دفعة",
            f"المورد: {self.supplier_name}\n\nأدخل مبلغ الدفعة:",
            minvalue=0.01
        )

        if amount:
            try:
                self.db.add_payment(
                    supplier_id=self.supplier_id,
                    amount=amount,
                    payment_type='دفع',
                    payment_method='نقدي',
                    notes=f"دفعة للمورد {self.supplier_name}"
                )
                messagebox.showinfo("نجح", f"تم تسجيل الدفعة بمبلغ {amount:.2f} {CURRENCY_SYMBOL}")
                self.refresh_data()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تسجيل الدفعة: {str(e)}")

    def print_statement(self):
        """طباعة كشف حساب المورد"""
        try:
            # إنشاء تقرير نصي مؤقت
            supplier = self.db.get_supplier_by_id(self.supplier_id)

            # حساب الإحصائيات
            conn = self.db.db_manager.get_connection()
            cursor = conn.cursor()

            # عدد المنتجات
            cursor.execute('SELECT COUNT(*) FROM products WHERE supplier_id = ?', (self.supplier_id,))
            products_count = cursor.fetchone()[0]

            # إجمالي المدفوعات
            cursor.execute('''
                SELECT COALESCE(SUM(amount), 0) FROM payments
                WHERE supplier_id = ? AND payment_type = 'دفع'
            ''', (self.supplier_id,))
            total_payments = cursor.fetchone()[0]

            conn.close()

            report_text = f"كشف حساب المورد: {self.supplier_name}\n"
            report_text += f"{'='*50}\n\n"
            report_text += f"عدد المنتجات: {products_count}\n"
            report_text += f"إجمالي المدفوعات: {total_payments:.2f} {CURRENCY_SYMBOL}\n"
            report_text += f"الهاتف: {supplier['phone'] or 'غير محدد'}\n"
            report_text += f"العنوان: {supplier['address'] or 'غير محدد'}\n"

            messagebox.showinfo("كشف الحساب", report_text)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء كشف الحساب: {str(e)}")

    def refresh_data(self):
        """تحديث جميع البيانات"""
        self.load_supplier_data()
