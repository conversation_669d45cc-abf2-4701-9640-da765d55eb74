# -*- coding: utf-8 -*-
"""
اختبار شامل للنظام - البيع بالدين وترحيل الديون
"""

import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.database import PharmacyDatabase

def test_complete_credit_system():
    """اختبار شامل لنظام البيع بالدين وترحيل الديون"""
    print("🧪 اختبار شامل لنظام البيع بالدين وترحيل الديون...")
    
    try:
        # إنشاء قاعدة بيانات اختبار
        db = PharmacyDatabase("test_complete_system.db")
        print("✅ تم إنشاء قاعدة البيانات بنجاح")
        
        # إضافة مورد
        supplier_id = db.add_supplier(
            name="مورد الأدوية",
            phone="0501234567",
            address="الرياض، المملكة العربية السعودية",
            email="<EMAIL>"
        )
        print(f"✅ تم إضافة مورد بالمعرف: {supplier_id}")
        
        # إضافة منتجات
        product1_id = db.add_product(
            name="باراسيتامول",
            category="أدوية",
            unit="حبة",
            purchase_price=0.5,
            selling_price=1.0,
            stock_quantity=1000,
            min_stock_level=50,
            supplier_id=supplier_id
        )
        
        product2_id = db.add_product(
            name="مبيد حشري",
            category="مبيدات",
            unit="لتر",
            purchase_price=25.0,
            selling_price=40.0,
            stock_quantity=100,
            min_stock_level=10,
            supplier_id=supplier_id
        )
        print(f"✅ تم إضافة منتجين بالمعرفات: {product1_id}, {product2_id}")
        
        # إضافة زبائن
        customer1_id = db.add_customer(
            name="مزرعة الخير",
            phone="0509876543",
            address="القصيم، المملكة العربية السعودية",
            email="<EMAIL>",
            credit_limit=5000.0
        )
        
        customer2_id = db.add_customer(
            name="أحمد المزارع",
            phone="0551234567",
            address="حائل، المملكة العربية السعودية",
            credit_limit=2000.0
        )
        print(f"✅ تم إضافة زبونين بالمعرفات: {customer1_id}, {customer2_id}")
        
        # اختبار البيع بالدين الكامل
        print("\n📝 اختبار البيع بالدين الكامل...")
        sale1_id = db.add_sale(
            customer_id=customer1_id,
            total_amount=500.0,
            paid_amount=0.0,  # بيع بالدين كامل
            payment_method="دين",
            notes="بيع بالدين كامل - اختبار"
        )
        
        # إضافة عناصر البيع
        db.add_sale_item(sale1_id, product1_id, 100, 1.0)  # 100 حبة × 1 = 100
        db.add_sale_item(sale1_id, product2_id, 10, 40.0)  # 10 لتر × 40 = 400
        
        # تحديث دين الزبون
        db.update_customer_debt(customer1_id, 500.0)
        print(f"✅ تم إنشاء بيع بالدين برقم: {sale1_id}")
        
        # اختبار البيع الجزئي
        print("\n💳 اختبار البيع الجزئي...")
        sale2_id = db.add_sale(
            customer_id=customer2_id,
            total_amount=200.0,
            paid_amount=50.0,  # دفع جزئي
            payment_method="جزئي",
            notes="بيع جزئي - اختبار"
        )
        
        # إضافة عناصر البيع الجزئي
        db.add_sale_item(sale2_id, product1_id, 50, 1.0)   # 50 حبة × 1 = 50
        db.add_sale_item(sale2_id, product2_id, 3, 40.0)   # 3 لتر × 40 = 120
        # المجموع: 170، لكن سنضع 200 للاختبار
        
        # تحديث دين الزبون للمبلغ المتبقي
        remaining_amount = 200.0 - 50.0  # 150
        db.update_customer_debt(customer2_id, remaining_amount)
        print(f"✅ تم إنشاء بيع جزئي برقم: {sale2_id}")
        
        # التحقق من الديون
        print("\n💰 التحقق من الديون...")
        customer1 = db.get_customer_by_id(customer1_id)
        customer2 = db.get_customer_by_id(customer2_id)
        
        print(f"✅ دين الزبون الأول (مزرعة الخير): {customer1['current_debt']:.2f} ريال")
        print(f"✅ دين الزبون الثاني (أحمد المزارع): {customer2['current_debt']:.2f} ريال")
        
        # اختبار الحصول على الزبائن المدينين
        print("\n👥 اختبار جلب الزبائن المدينين...")
        debtors = db.get_customers_with_debts()
        print(f"✅ عدد الزبائن المدينين: {len(debtors)}")
        
        for debtor in debtors:
            print(f"   - {debtor['name']}: {debtor['current_debt']:.2f} ريال")
        
        # اختبار دفعة من الزبون الأول
        print("\n💰 اختبار دفعة من الزبون الأول...")
        payment1_id = db.add_payment(
            customer_id=customer1_id,
            amount=200.0,
            payment_type="استلام",
            payment_method="نقدي",
            notes="دفعة نقدية من مزرعة الخير"
        )
        print(f"✅ تم تسجيل دفعة برقم: {payment1_id}")
        
        # التحقق من الدين بعد الدفعة
        customer1_after = db.get_customer_by_id(customer1_id)
        print(f"✅ دين الزبون الأول بعد الدفعة: {customer1_after['current_debt']:.2f} ريال")
        
        # اختبار دفعة جزئية من الزبون الثاني
        print("\n💳 اختبار دفعة جزئية من الزبون الثاني...")
        payment2_id = db.add_payment(
            customer_id=customer2_id,
            amount=100.0,
            payment_type="استلام",
            payment_method="تحويل بنكي",
            notes="دفعة جزئية من أحمد المزارع"
        )
        print(f"✅ تم تسجيل دفعة جزئية برقم: {payment2_id}")
        
        # التحقق من الدين بعد الدفعة الجزئية
        customer2_after = db.get_customer_by_id(customer2_id)
        print(f"✅ دين الزبون الثاني بعد الدفعة: {customer2_after['current_debt']:.2f} ريال")
        
        # اختبار الحصول على مدفوعات الزبائن
        print("\n📊 اختبار جلب مدفوعات الزبائن...")
        payments1 = db.get_customer_payments(customer1_id)
        payments2 = db.get_customer_payments(customer2_id)
        
        print(f"✅ عدد مدفوعات الزبون الأول: {len(payments1)}")
        print(f"✅ عدد مدفوعات الزبون الثاني: {len(payments2)}")
        
        # اختبار الحصول على المبيعات
        print("\n📋 اختبار جلب المبيعات...")
        sales = db.get_all_sales()
        print(f"✅ إجمالي المبيعات: {len(sales)}")
        
        for sale in sales:
            customer_name = sale['customer_name'] or 'زبون نقدي'
            print(f"   - بيع رقم {sale['id']}: {customer_name} - {sale['total_amount']:.2f} ريال (مدفوع: {sale['paid_amount']:.2f}, متبقي: {sale['remaining_amount']:.2f})")
        
        # اختبار تفاصيل المبيعات
        print("\n🔍 اختبار تفاصيل المبيعات...")
        sale1_items = db.get_sale_items(sale1_id)
        print(f"✅ عناصر البيع الأول: {len(sale1_items)}")
        
        for item in sale1_items:
            print(f"   - {item['product_name']}: {item['quantity']} {item['unit']} × {item['unit_price']:.2f} = {item['total_price']:.2f} ريال")
        
        # التحقق من المخزون بعد المبيعات
        print("\n📦 التحقق من المخزون بعد المبيعات...")
        product1 = db.get_product_by_id(product1_id)
        product2 = db.get_product_by_id(product2_id)
        
        print(f"✅ مخزون {product1['name']}: {product1['stock_quantity']} {product1['unit']}")
        print(f"✅ مخزون {product2['name']}: {product2['stock_quantity']} {product2['unit']}")
        
        # ملخص نهائي
        print("\n📈 ملخص نهائي للنظام...")
        all_customers = db.get_all_customers()
        total_debt = sum(customer['current_debt'] for customer in all_customers)
        
        print(f"✅ إجمالي عدد الزبائن: {len(all_customers)}")
        print(f"✅ إجمالي الديون: {total_debt:.2f} ريال")
        print(f"✅ عدد الزبائن المدينين: {len(debtors)}")
        print(f"✅ إجمالي المبيعات: {len(sales)}")
        
        # تنظيف ملف الاختبار
        os.remove("test_complete_system.db")
        print("\n🗑️ تم حذف قاعدة بيانات الاختبار")
        
        print("\n🎉 جميع اختبارات النظام الشامل نجحت!")
        print("✅ البيع بالدين يعمل بشكل مثالي")
        print("✅ ترحيل الديون يعمل بشكل صحيح")
        print("✅ تحديث المخزون يعمل تلقائياً")
        print("✅ تسجيل المدفوعات يعمل بشكل سليم")
        
        return True
        
    except Exception as e:
        print(f"\n❌ فشل الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # تنظيف في حالة الخطأ
        try:
            os.remove("test_complete_system.db")
        except:
            pass
        
        return False

if __name__ == "__main__":
    test_complete_credit_system()
